import express from 'express';
import { protect } from '../middleware/auth';

const router = express.Router();

// Placeholder routes - to be implemented
router.get('/', (req, res) => {
  res.json({ message: 'Job postings - À implémenter' });
});

router.get('/:id', (req, res) => {
  res.json({ message: 'Get job by ID - À implémenter' });
});

router.post('/', protect, (req, res) => {
  res.json({ message: 'Create job posting - À implémenter' });
});

export default router;