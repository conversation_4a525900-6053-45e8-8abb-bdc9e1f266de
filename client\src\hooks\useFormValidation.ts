import { useState, useCallback, useMemo } from 'react';
import { FieldValidation, validateField, validateForm } from '@/lib/validation';

interface UseFormValidationOptions {
  validationRules: FieldValidation;
  initialData?: any;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

interface FormField {
  value: any;
  error?: string;
  touched: boolean;
}

interface UseFormValidationReturn {
  formData: any;
  errors: { [key: string]: string };
  touched: { [key: string]: boolean };
  isValid: boolean;
  isSubmitting: boolean;
  setValue: (field: string, value: any) => void;
  setError: (field: string, error: string) => void;
  clearError: (field: string) => void;
  clearAllErrors: () => void;
  setTouched: (field: string, touched?: boolean) => void;
  validateField: (field: string) => string | null;
  validateAll: () => boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleBlur: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSubmit: (onSubmit: (data: any) => Promise<void> | void) => (e: React.FormEvent) => Promise<void>;
  reset: (newData?: any) => void;
  setIsSubmitting: (submitting: boolean) => void;
}

export function useFormValidation({
  validationRules,
  initialData = {},
  validateOnChange = false,
  validateOnBlur = true
}: UseFormValidationOptions): UseFormValidationReturn {
  const [formData, setFormData] = useState(initialData);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [touched, setTouchedState] = useState<{ [key: string]: boolean }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0 && Object.keys(formData).length > 0;
  }, [errors, formData]);

  const setValue = useCallback((field: string, value: any) => {
    setFormData((prev: any) => ({ ...prev, [field]: value }));
    
    if (validateOnChange && validationRules[field]) {
      const error = validateField(value, validationRules[field], { ...formData, [field]: value });
      setErrors((prev: any) => ({
        ...prev,
        [field]: error || ''
      }));
    }
  }, [formData, validationRules, validateOnChange]);

  const setError = useCallback((field: string, error: string) => {
    setErrors((prev: any) => ({ ...prev, [field]: error }));
  }, []);

  const clearError = useCallback((field: string) => {
    setErrors((prev: any) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  const setTouched = useCallback((field: string, isTouched: boolean = true) => {
    setTouchedState((prev: any) => ({ ...prev, [field]: isTouched }));
  }, []);

  const validateFieldFn = useCallback((field: string): string | null => {
    if (!validationRules[field]) return null;
    
    const error = validateField(formData[field], validationRules[field], formData);
    
    if (error) {
      setError(field, error);
    } else {
      clearError(field);
    }
    
    return error;
  }, [formData, validationRules, setError, clearError]);

  const validateAll = useCallback((): boolean => {
    const newErrors = validateForm(formData, validationRules);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, validationRules]);

  const handleChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    const newValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;
    
    setValue(name, newValue);
    
    // Clear error when user starts typing
    if (errors[name]) {
      clearError(name);
    }
  }, [setValue, errors, clearError]);

  const handleBlur = useCallback((
    e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name } = e.target;
    setTouched(name, true);
    
    if (validateOnBlur) {
      validateFieldFn(name);
    }
  }, [setTouched, validateOnBlur, validateFieldFn]);

  const handleSubmit = useCallback((
    onSubmit: (data: any) => Promise<void> | void
  ) => {
    return async (e: React.FormEvent) => {
      e.preventDefault();
      
      if (!validateAll()) {
        // Mark all fields as touched to show errors
        const touchedFields: { [key: string]: boolean } = {};
        Object.keys(validationRules).forEach(field => {
          touchedFields[field] = true;
        });
        setTouchedState(touchedFields);
        return;
      }

      setIsSubmitting(true);
      try {
        await onSubmit(formData);
      } catch (error) {
        // Handle submission errors
        if (error instanceof Error) {
          setError('submit', error.message);
        } else {
          setError('submit', 'Une erreur inattendue s\'est produite');
        }
      } finally {
        setIsSubmitting(false);
      }
    };
  }, [formData, validateAll, validationRules, setError]);

  const reset = useCallback((newData?: any) => {
    setFormData(newData || initialData);
    setErrors({});
    setTouchedState({});
    setIsSubmitting(false);
  }, [initialData]);

  return {
    formData,
    errors,
    touched,
    isValid,
    isSubmitting,
    setValue,
    setError,
    clearError,
    clearAllErrors,
    setTouched,
    validateField: validateFieldFn,
    validateAll,
    handleChange,
    handleBlur,
    handleSubmit,
    reset,
    setIsSubmitting
  };
}