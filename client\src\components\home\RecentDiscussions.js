'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { MessageSquare, ThumbsUp, Clock, User } from 'lucide-react';

// Mock data - will be replaced with API calls
const mockDiscussions = [
  {
    id: 1,
    title: "Amélioration de l'éclairage public",
    excerpt: "Que pensez-vous de l'installation de nouveaux lampadaires LED dans le centre-ville ?",
    author: "<PERSON>",
    category: "Aménagement urbain",
    replies: 12,
    likes: 8,
    lastActivity: "2024-01-15T10:30:00Z",
    isSticky: true
  },
  {
    id: 2,
    title: "Organisation covoiturage école",
    excerpt: "Bon<PERSON>r, je cherche à organiser un système de covoiturage pour l'école primaire...",
    author: "<PERSON>",
    category: "Entraide",
    replies: 5,
    likes: 15,
    lastActivity: "2024-01-14T16:45:00Z",
    isSticky: false
  },
  {
    id: 3,
    title: "Nouveau commerce rue principale",
    excerpt: "Avez-vous vu le nouveau magasin qui s'installe ? Quelqu'un sait ce que ce sera ?",
    author: "<PERSON>",
    category: "Commerce local",
    replies: 23,
    likes: 6,
    lastActivity: "2024-01-14T12:20:00Z",
    isSticky: false
  }
];

export function RecentDiscussions() {
  const t = useTranslations('forum');

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Il y a moins d\'une heure';
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
  };

  return (
    <div className="space-y-4">
      {mockDiscussions.map((discussion) => (
        <div key={discussion.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-2">
              <span className="text-xs font-medium text-purple-600 bg-purple-50 px-2 py-1 rounded">
                {discussion.category}
              </span>
              {discussion.isSticky && (
                <span className="text-xs font-medium text-red-600 bg-red-50 px-2 py-1 rounded">
                  Épinglé
                </span>
              )}
            </div>
            <div className="flex items-center text-xs text-gray-500">
              <Clock size={12} className="mr-1" />
              {getTimeAgo(discussion.lastActivity)}
            </div>
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            <Link href={`/forum/discussion/${discussion.id}`} className="hover:text-primary-600">
              {discussion.title}
            </Link>
          </h3>
          
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {discussion.excerpt}
          </p>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <User size={14} />
                <span>{discussion.author}</span>
              </div>
              <div className="flex items-center space-x-1">
                <MessageSquare size={14} />
                <span>{discussion.replies} réponses</span>
              </div>
              <div className="flex items-center space-x-1">
                <ThumbsUp size={14} />
                <span>{discussion.likes}</span>
              </div>
            </div>
            
            <Link 
              href={`/forum/discussion/${discussion.id}`}
              className="text-primary-600 hover:text-primary-700 font-medium text-sm"
            >
              Participer
            </Link>
          </div>
        </div>
      ))}
      
      <div className="text-center pt-4">
        <Link 
          href="/forum"
          className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium"
        >
          Voir toutes les discussions
          <MessageSquare size={16} className="ml-2" />
        </Link>
      </div>
    </div>
  );
}