import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get all businesses with optional filtering
export const getBusinesses = async (req: Request, res: Response) => {
  try {
    const { 
      category, 
      verified, 
      search, 
      page = '1', 
      limit = '12' 
    } = req.query;
    
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const where: any = {
      isActive: true
    };
    
    // Filter by category if provided
    if (category && category !== 'all') {
      where.category = category;
    }
    
    // Filter by verification status if provided
    if (verified === 'true') {
      where.isVerified = true;
    }
    
    // Search in name and description if provided
    if (search) {
      where.OR = [
        {
          name: {
            contains: search as string,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: search as string,
            mode: 'insensitive'
          }
        }
      ];
    }

    const [businesses, totalCount] = await Promise.all([
      prisma.business.findMany({
        where,
        include: {
          owner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true
            }
          },
          reviews: {
            select: {
              id: true,
              rating: true
            }
          },
          _count: {
            select: {
              reviews: true,
              products: true
            }
          }
        },
        orderBy: [
          { isVerified: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limitNum
      }),
      prisma.business.count({ where })
    ]);

    // Calculate average rating for each business
    const businessesWithRating = businesses.map(business => {
      const avgRating = business.reviews.length > 0 
        ? business.reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / business.reviews.length
        : 0;
      
      const { reviews, ...businessData } = business;
      return {
        ...businessData,
        averageRating: Math.round(avgRating * 10) / 10 // Round to 1 decimal
      };
    });

    res.json({
      success: true,
      data: businessesWithRating,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        pages: Math.ceil(totalCount / limitNum)
      }
    });
  } catch (error) {
    console.error('Error fetching businesses:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des entreprises'
    });
  }
};

// Get business by ID with detailed information
export const getBusinessById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const business = await prisma.business.findUnique({
      where: { id },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
            email: true
          }
        },
        products: {
          where: {
            isActive: true
          },
          select: {
            id: true,
            name: true,
            price: true,
            images: true,
            category: true
          },
          take: 6 // Show latest 6 products
        },
        reviews: {
          include: {
            author: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        _count: {
          select: {
            reviews: true,
            products: true
          }
        }
      }
    });

    if (!business || !business.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }

    // Calculate average rating
    const avgRating = business.reviews.length > 0 
      ? business.reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / business.reviews.length
      : 0;

    const businessWithRating = {
      ...business,
      averageRating: Math.round(avgRating * 10) / 10
    };

    res.json({
      success: true,
      data: businessWithRating
    });
  } catch (error) {
    console.error('Error fetching business:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'entreprise'
    });
  }
};

// Create new business (authenticated users)
export const createBusiness = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const {
      name,
      description,
      category,
      address,
      phone,
      email,
      website,
      hours,
      logo,
      images
    } = req.body;

    const userId = (req as any).user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const business = await prisma.business.create({
      data: {
        name,
        description,
        category,
        address,
        phone,
        email,
        website,
        hours: hours ? JSON.parse(hours) : null,
        logo,
        images: images || [],
        ownerId: userId
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        _count: {
          select: {
            reviews: true,
            products: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Entreprise créée avec succès',
      data: {
        ...business,
        averageRating: 0
      }
    });
  } catch (error) {
    console.error('Error creating business:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de l\'entreprise'
    });
  }
};

// Update business (owner or admin only)
export const updateBusiness = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if business exists
    const existingBusiness = await prisma.business.findUnique({
      where: { id }
    });

    if (!existingBusiness) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }

    // Check if user is owner or admin
    if (existingBusiness.ownerId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    const updateData: any = {};
    const {
      name,
      description,
      category,
      address,
      phone,
      email,
      website,
      hours,
      logo,
      images,
      isVerified,
      isActive
    } = req.body;

    // Only update provided fields
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (category !== undefined) updateData.category = category;
    if (address !== undefined) updateData.address = address;
    if (phone !== undefined) updateData.phone = phone;
    if (email !== undefined) updateData.email = email;
    if (website !== undefined) updateData.website = website;
    if (hours !== undefined) updateData.hours = hours ? JSON.parse(hours) : null;
    if (logo !== undefined) updateData.logo = logo;
    if (images !== undefined) updateData.images = images;
    
    // Admin-only fields
    if (userRole === 'ADMIN') {
      if (isVerified !== undefined) updateData.isVerified = isVerified;
      if (isActive !== undefined) updateData.isActive = isActive;
    }

    const business = await prisma.business.update({
      where: { id },
      data: updateData,
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        reviews: {
          select: {
            rating: true
          }
        },
        _count: {
          select: {
            reviews: true,
            products: true
          }
        }
      }
    });

    // Calculate average rating
    const avgRating = business.reviews.length > 0 
      ? business.reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / business.reviews.length
      : 0;

    const { reviews, ...businessData } = business;

    res.json({
      success: true,
      message: 'Entreprise mise à jour avec succès',
      data: {
        ...businessData,
        averageRating: Math.round(avgRating * 10) / 10
      }
    });
  } catch (error) {
    console.error('Error updating business:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de l\'entreprise'
    });
  }
};

// Delete business (owner or admin only)
export const deleteBusiness = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if business exists
    const existingBusiness = await prisma.business.findUnique({
      where: { id }
    });

    if (!existingBusiness) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }

    // Check if user is owner or admin
    if (existingBusiness.ownerId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    // Soft delete by setting isActive to false
    await prisma.business.update({
      where: { id },
      data: {
        isActive: false
      }
    });

    res.json({
      success: true,
      message: 'Entreprise supprimée avec succès'
    });
  } catch (error) {
    console.error('Error deleting business:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'entreprise'
    });
  }
};

// Get business reviews
export const getBusinessReviews = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { page = '1', limit = '10' } = req.query;
    
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Check if business exists
    const business = await prisma.business.findUnique({
      where: { id },
      select: { id: true, name: true }
    });

    if (!business) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }

    const [reviews, totalCount] = await Promise.all([
      prisma.review.findMany({
        where: {
          businessId: id
        },
        include: {
          author: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limitNum
      }),
      prisma.review.count({
        where: {
          businessId: id
        }
      })
    ]);

    res.json({
      success: true,
      data: reviews,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        pages: Math.ceil(totalCount / limitNum)
      }
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des avis'
    });
  }
};

// Add review to business
export const addReview = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { id } = req.params; // Business ID
    const { rating, comment } = req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Check if business exists
    const business = await prisma.business.findUnique({
      where: { id }
    });

    if (!business || !business.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }

    // Check if user already reviewed this business
    const existingReview = await prisma.review.findFirst({
      where: {
        authorId: userId,
        businessId: id
      }
    });

    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'Vous avez déjà donné votre avis sur cette entreprise'
      });
    }

    const review = await prisma.review.create({
      data: {
        rating: parseInt(rating),
        comment,
        authorId: userId,
        businessId: id
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Avis ajouté avec succès',
      data: review
    });
  } catch (error) {
    console.error('Error adding review:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout de l\'avis'
    });
  }
};

// Update review (author only)
export const updateReview = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { reviewId } = req.params;
    const { rating, comment } = req.body;
    const userId = (req as any).user?.id;

    // Check if review exists
    const existingReview = await prisma.review.findUnique({
      where: { id: reviewId }
    });

    if (!existingReview) {
      return res.status(404).json({
        success: false,
        message: 'Avis non trouvé'
      });
    }

    // Check if user is author
    if (existingReview.authorId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    const review = await prisma.review.update({
      where: { id: reviewId },
      data: {
        rating: rating ? parseInt(rating) : existingReview.rating,
        comment: comment !== undefined ? comment : existingReview.comment
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Avis mis à jour avec succès',
      data: review
    });
  } catch (error) {
    console.error('Error updating review:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de l\'avis'
    });
  }
};

// Delete review (author or admin)
export const deleteReview = async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if review exists
    const existingReview = await prisma.review.findUnique({
      where: { id: reviewId }
    });

    if (!existingReview) {
      return res.status(404).json({
        success: false,
        message: 'Avis non trouvé'
      });
    }

    // Check if user is author or admin
    if (existingReview.authorId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    await prisma.review.delete({
      where: { id: reviewId }
    });

    res.json({
      success: true,
      message: 'Avis supprimé avec succès'
    });
  } catch (error) {
    console.error('Error deleting review:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'avis'
    });
  }
};

// Get business categories
export const getBusinessCategories = async (req: Request, res: Response) => {
  try {
    // Get unique categories from businesses
    const categories = await prisma.business.findMany({
      where: {
        isActive: true
      },
      select: {
        category: true
      },
      distinct: ['category']
    });

    const uniqueCategories = categories.map(b => b.category).sort();

    res.json({
      success: true,
      data: uniqueCategories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des catégories'
    });
  }
};