export default function EmploisPage() {
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Offres d'Emploi</h1>
      
      <div className="grid gap-6">
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Serveur/Serveuse</h2>
          <p className="text-gray-600 mb-4">Restaurant Le Gourmet recherche un(e) serveur/serveuse dynamique pour rejoindre notre équipe.</p>
          <div className="text-sm text-gray-500 mb-4">
            <p>🏢 Restaurant Le Gourmet</p>
            <p>📍 Centre-ville</p>
            <p>💰 12-15€/heure</p>
            <p>⏰ Temps partiel</p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded">Postuler</button>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Mécanicien Automobile</h2>
          <p className="text-gray-600 mb-4">Poste de mécanicien pour entretien et réparation tous véhicules. CAP mécanique requis.</p>
          <div className="text-sm text-gray-500 mb-4">
            <p>🏢 Garage Martin</p>
            <p>📍 Zone Industrielle</p>
            <p>💰 2000-2500€/mois</p>
            <p>⏰ Temps plein</p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded">Postuler</button>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Développeur Web Junior</h2>
          <p className="text-gray-600 mb-4">Nous cherchons un développeur junior passionné pour rejoindre notre startup innovante.</p>
          <div className="text-sm text-gray-500 mb-4">
            <p>🏢 TechStart</p>
            <p>📍 Centre-ville</p>
            <p>💰 2200-2800€/mois</p>
            <p>⏰ Temps plein</p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded">Postuler</button>
        </div>
      </div>
    </div>
  );
}