version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: leclub-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: leclub_db
      POSTGRES_USER: leclub_user
      POSTGRES_PASSWORD: leclub_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - leclub-network

  server:
    build:
      context: ./server
      dockerfile: Dockerfile.dev
    container_name: leclub-server
    restart: unless-stopped
    environment:
      DATABASE_URL: "******************************************************/leclub_db"
      JWT_SECRET: "leclub-super-secret-jwt-key-change-this-in-production-2024"
      JWT_EXPIRES_IN: "7d"
      PORT: 3001
      NODE_ENV: "development"
      CORS_ORIGIN: "http://localhost:3000"
    ports:
      - "3001:3001"
    volumes:
      - ./server:/app
      - /app/node_modules
    depends_on:
      - postgres
    networks:
      - leclub-network
    command: npm run dev

  client:
    build:
      context: ./client
      dockerfile: Dockerfile.dev
    container_name: leclub-client
    restart: unless-stopped
    environment:
      NEXT_PUBLIC_API_URL: "http://localhost:3001"
    ports:
      - "3000:3000"
    volumes:
      - ./client:/app
      - /app/node_modules
      - /app/.next
    networks:
      - leclub-network
    command: npm run dev

volumes:
  postgres_data:
    driver: local

networks:
  leclub-network:
    driver: bridge