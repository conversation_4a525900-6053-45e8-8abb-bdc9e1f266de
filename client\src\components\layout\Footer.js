'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { MapPin, Phone, Mail, Facebook, Twitter, Instagram } from 'lucide-react';

export function Footer() {
  const t = useTranslations('navigation');

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">LC</span>
              </div>
              <span className="text-xl font-bold">LeClub</span>
            </div>
            <p className="text-gray-400 text-sm">
              Votre communauté locale, connectée et engagée.
            </p>
            <div className="flex space-x-4">
              <Facebook size={20} className="text-gray-400 hover:text-white cursor-pointer" />
              <Twitter size={20} className="text-gray-400 hover:text-white cursor-pointer" />
              <Instagram size={20} className="text-gray-400 hover:text-white cursor-pointer" />
            </div>
          </div>

          {/* Navigation */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Navigation</h3>
            <ul className="space-y-2">
              <li><Link href="/" className="text-gray-400 hover:text-white">{t('home')}</Link></li>
              <li><Link href="/actualites" className="text-gray-400 hover:text-white">{t('news')}</Link></li>
              <li><Link href="/evenements" className="text-gray-400 hover:text-white">{t('events')}</Link></li>
              <li><Link href="/forum" className="text-gray-400 hover:text-white">{t('forum')}</Link></li>
            </ul>
          </div>

          {/* Marketplace */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Marché</h3>
            <ul className="space-y-2">
              <li><Link href="/marche" className="text-gray-400 hover:text-white">{t('marketplace')}</Link></li>
              <li><Link href="/entreprises" className="text-gray-400 hover:text-white">{t('businesses')}</Link></li>
              <li><Link href="/emplois" className="text-gray-400 hover:text-white">{t('jobs')}</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-gray-400">
                <MapPin size={16} />
                <span className="text-sm">123 Rue Principale, Ville, QC</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-400">
                <Phone size={16} />
                <span className="text-sm">+****************</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-400">
                <Mail size={16} />
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400 text-sm">
            © 2024 LeClub. Tous droits réservés. Plateforme communautaire locale.
          </p>
        </div>
      </div>
    </footer>
  );
}