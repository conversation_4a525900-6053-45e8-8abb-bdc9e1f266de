// Server Component with async data fetching
async function getEvenements() {
  // Simulate server-side API call
  await new <PERSON>(resolve => setTimeout(resolve, 80));
  
  return [
    {
      id: 1,
      title: "Marché de producteurs locaux",
      description: "Découvrez les meilleurs produits locaux directement chez les producteurs de la région.",
      date: "15 février 2024",
      time: "08:00 - 12:00", 
      location: "Place du Marché",
      participants: { current: 45, max: 100 }
    },
    {
      id: 2,
      title: "Concert de jazz au parc",
      description: "Soirée musicale avec le trio jazz local 'Les Nocturnes' dans le cadre verdoyant du parc municipal.",
      date: "18 février 2024",
      time: "19:30 - 21:30",
      location: "Kiosque du Parc Municipal", 
      participants: { current: 78, max: 150 }
    },
    {
      id: 3,
      title: "Tournoi de football amateur",
      description: "Championnat inter-quartiers ouvert à tous les niveaux. Inscriptions sur place.",
      date: "20 février 2024",
      time: "14:00 - 18:00",
      location: "Stade Municipal",
      participants: { current: 32, max: 64 }
    }
  ];
}

export default async function EvenementsPage() {
  // Server-side data fetching
  const evenements = await getEvenements();
  
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Calendrier des Événements</h1>
      
      <div className="grid gap-6">
        {evenements.map((evenement) => (
          <article key={evenement.id} className="border p-6 rounded-lg hover:shadow-md transition-shadow">
            <h2 className="text-xl font-semibold mb-2">{evenement.title}</h2>
            <p className="text-gray-600 mb-4">{evenement.description}</p>
            <div className="text-sm text-gray-500 space-y-1">
              <p>📅 {evenement.date} • {evenement.time}</p>
              <p>📍 {evenement.location}</p>
              <p>👥 {evenement.participants.current}/{evenement.participants.max} participants</p>
            </div>
          </article>
        ))}
      </div>
    </div>
  );
}