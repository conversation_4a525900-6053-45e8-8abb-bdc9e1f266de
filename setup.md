# Configuration Initiale - LeClub

Les erreurs que vous voyez sont normales lors du premier démarrage. Voici comment les résoudre :

## 🔧 **Étapes de configuration manquantes**

### 1. Base de données PostgreSQL

Vous devez d'abord configurer PostgreSQL :

```bash
# Créer une base de données (si pas encore fait)
createdb leclub_db

# Ou via psql
psql -U postgres
CREATE DATABASE leclub_db;
\q
```

### 2. Variables d'environnement

```bash
# Copier les fichiers d'exemple
cp server/.env.example server/.env
cp client/.env.example client/.env.local

# Éditer server/.env avec vos informations
# DATABASE_URL="postgresql://username:password@localhost:5432/leclub_db"
# JWT_SECRET="votre-clé-secrète"
```

### 3. Migration de la base de données

```bash
cd server
npx prisma migrate dev --name init
npx prisma generate
cd ..
```

### 4. Installation des dépendances manquantes

```bash
# Dans le serveur
cd server && npm install && cd ..

# Redémarrer le projet
npm run dev
```

## 🚨 **Erreurs résolues**

✅ **Erreur module '@/routes/auth'** - Corrigée avec tsconfig-paths
✅ **Warning Next.js appDir** - Supprimé (plus nécessaire dans Next.js 14)

## 🎯 **Prochaines étapes**

Une fois la base de données configurée :
1. L'API sera accessible sur http://localhost:3001
2. Le frontend sur http://localhost:3000
3. Test : http://localhost:3001/health doit retourner "OK"

Faites-moi savoir quand c'est fait et je pourrai vous aider avec la suite !