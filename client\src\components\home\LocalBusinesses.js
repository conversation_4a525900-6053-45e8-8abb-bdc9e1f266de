'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Star, MapPin, Clock, Phone, Shield } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Business } from '@/lib/types';
import api from '@/lib/api';

export function LocalBusinesses() {
  const t = useTranslations('businesses');
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBusinesses = async () => {
      try {
        setLoading(true);
        const response = await api.getBusinesses({
          limit: 6,
          verified: true // Show verified businesses first
        });
        
        if (response.success && response.data) {
          setBusinesses(response.data);
        } else {
          setError('Erreur lors du chargement des entreprises');
        }
      } catch (err) {
        console.error('Error fetching businesses:', err);
        setError('Erreur lors du chargement des entreprises');
      } finally {
        setLoading(false);
      }
    };

    fetchBusinesses();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
            <div className="h-48 bg-gray-200"></div>
            <div className="p-6">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-6 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="mt-4 text-primary-600 hover:text-primary-700"
        >
          Réessayer
        </button>
      </div>
    );
  }

  if (businesses.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Aucune entreprise trouvée pour le moment.</p>
        <Link 
          href="/entreprises" 
          className="mt-4 inline-block text-primary-600 hover:text-primary-700"
        >
          Voir toutes les entreprises
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {businesses.map((business) => (
        <div key={business.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
          <div className="h-48 bg-gray-200 flex items-center justify-center relative">
            {business.images && business.images.length > 0 ? (
              <img 
                src={business.images[0]} 
                alt={business.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const img = e.currentTarget as HTMLImageElement;
                  const fallback = img.nextElementSibling as HTMLElement;
                  img.style.display = 'none';
                  if (fallback) fallback.style.display = 'flex';
                }}
              />
            ) : null}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-gray-500">Image à venir</span>
            </div>
            {business.isVerified && (
              <div className="absolute top-2 right-2">
                <Shield size={16} className="text-green-600 fill-current bg-white rounded-full p-1" />
              </div>
            )}
          </div>
          
          <div className="p-6">
            <div className="flex items-start justify-between mb-2">
              <span className="text-xs font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded">
                {business.category}
              </span>
              <div className="flex items-center space-x-1">
                <Star size={14} className="text-yellow-400 fill-current" />
                <span className="text-sm font-medium">
                  {business.averageRating > 0 ? business.averageRating.toFixed(1) : '0.0'}
                </span>
                <span className="text-xs text-gray-500">
                  ({business._count.reviews})
                </span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {business.name}
              </h3>
              {business.isVerified && (
                <Shield size={16} className="text-green-600 fill-current" />
              )}
            </div>
            
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {business.description || 'Aucune description disponible.'}
            </p>
            
            <div className="space-y-1 mb-4">
              <div className="flex items-center text-sm text-gray-500">
                <MapPin size={14} className="mr-2 flex-shrink-0" />
                <span className="truncate">{business.address}</span>
              </div>
              {business.phone && (
                <div className="flex items-center text-sm text-gray-500">
                  <Phone size={14} className="mr-2 flex-shrink-0" />
                  <span>{business.phone}</span>
                </div>
              )}
              <div className="flex items-center text-sm text-gray-500">
                <Clock size={14} className="mr-2 flex-shrink-0" />
                <span>Voir les horaires</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <Link 
                href={`/entreprises/${business.id}`}
                className="text-primary-600 hover:text-primary-700 font-medium text-sm"
              >
                Voir profil
              </Link>
              {business.email && (
                <a 
                  href={`mailto:${business.email}`}
                  className="bg-primary-600 text-white px-3 py-1 rounded text-sm hover:bg-primary-700 transition-colors"
                >
                  Contacter
                </a>
              )}
              {business.phone && !business.email && (
                <a 
                  href={`tel:${business.phone}`}
                  className="bg-primary-600 text-white px-3 py-1 rounded text-sm hover:bg-primary-700 transition-colors"
                >
                  Appeler
                </a>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}