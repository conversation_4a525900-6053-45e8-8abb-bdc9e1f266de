import React, { forwardRef } from 'react';
import { Eye, EyeOff } from 'lucide-react';

interface BaseFormFieldProps {
  label?: string;
  error?: string;
  touched?: boolean;
  required?: boolean;
  helpText?: string;
  className?: string;
  wrapperClassName?: string;
}

interface InputProps extends BaseFormFieldProps {
  name: string;
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'number';
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  autoComplete?: string;
  showPasswordToggle?: boolean;
}

interface TextareaProps extends BaseFormFieldProps {
  name: string;
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  rows?: number;
  maxLength?: number;
}

interface SelectProps extends BaseFormFieldProps {
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  disabled?: boolean;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  placeholder?: string;
}

const baseInputClasses = `
  w-full px-3 py-2 border rounded-lg text-sm
  focus:ring-2 focus:ring-primary-500 focus:border-primary-500
  disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
  transition-colors
`;

const errorClasses = 'border-red-300 focus:ring-red-500 focus:border-red-500';
const normalClasses = 'border-gray-300';

export const FormInput = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  touched,
  required,
  helpText,
  className = '',
  wrapperClassName = '',
  type = 'text',
  showPasswordToggle = false,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const inputType = type === 'password' && showPassword ? 'text' : type;
  const hasError = touched && error;

  return (
    <div className={`space-y-1 ${wrapperClassName}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={ref}
          type={inputType}
          className={`
            ${baseInputClasses}
            ${hasError ? errorClasses : normalClasses}
            ${showPasswordToggle ? 'pr-10' : ''}
            ${className}
          `}
          {...props}
        />
        
        {showPasswordToggle && type === 'password' && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            )}
          </button>
        )}
      </div>
      
      {hasError && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      
      {helpText && !hasError && (
        <p className="text-sm text-gray-500">{helpText}</p>
      )}
    </div>
  );
});

FormInput.displayName = 'FormInput';

export const FormTextarea = forwardRef<HTMLTextAreaElement, TextareaProps>(({
  label,
  error,
  touched,
  required,
  helpText,
  className = '',
  wrapperClassName = '',
  rows = 4,
  maxLength,
  ...props
}, ref) => {
  const hasError = touched && error;
  const currentLength = props.value?.length || 0;

  return (
    <div className={`space-y-1 ${wrapperClassName}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        ref={ref}
        rows={rows}
        maxLength={maxLength}
        className={`
          ${baseInputClasses}
          ${hasError ? errorClasses : normalClasses}
          resize-vertical
          ${className}
        `}
        {...props}
      />
      
      <div className="flex justify-between items-start">
        <div>
          {hasError && (
            <p className="text-sm text-red-600">{error}</p>
          )}
          
          {helpText && !hasError && (
            <p className="text-sm text-gray-500">{helpText}</p>
          )}
        </div>
        
        {maxLength && (
          <p className={`text-xs ${currentLength > maxLength * 0.9 ? 'text-orange-600' : 'text-gray-400'}`}>
            {currentLength}/{maxLength}
          </p>
        )}
      </div>
    </div>
  );
});

FormTextarea.displayName = 'FormTextarea';

export const FormSelect = forwardRef<HTMLSelectElement, SelectProps>(({
  label,
  error,
  touched,
  required,
  helpText,
  className = '',
  wrapperClassName = '',
  options,
  placeholder,
  ...props
}, ref) => {
  const hasError = touched && error;

  return (
    <div className={`space-y-1 ${wrapperClassName}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <select
        ref={ref}
        className={`
          ${baseInputClasses}
          ${hasError ? errorClasses : normalClasses}
          ${className}
        `}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>
      
      {hasError && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      
      {helpText && !hasError && (
        <p className="text-sm text-gray-500">{helpText}</p>
      )}
    </div>
  );
});

FormSelect.displayName = 'FormSelect';

interface FormButtonProps {
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const FormButton: React.FC<FormButtonProps> = ({
  type = 'button',
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  children,
  className = '',
  onClick
}) => {
  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2';
  
  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  return (
    <button
      type={type}
      disabled={disabled || loading}
      onClick={onClick}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${className}
      `}
    >
      {loading && (
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
      )}
      <span>{children}</span>
    </button>
  );
};