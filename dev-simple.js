#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting LeClub development environment...');

// Set environment variables for database connection through localhost
process.env.DATABASE_URL = "postgresql://leclub_user:leclub_password@localhost:5432/leclub_db";
process.env.JWT_SECRET = "leclub-super-secret-jwt-key-change-this-in-production-2024";
process.env.JWT_EXPIRES_IN = "7d";
process.env.PORT = "3001";
process.env.NODE_ENV = "development";
process.env.CORS_ORIGIN = "http://localhost:3000";

console.log('🔧 Starting API server...');

// Start the server
const serverProcess = spawn('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, 'server'),
  stdio: 'inherit',
  env: process.env
});

serverProcess.on('error', (error) => {
  console.error('❌ Server failed to start:', error);
});

serverProcess.on('exit', (code) => {
  console.log(`🛑 Server exited with code ${code}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});

console.log('✅ Development environment starting...');
console.log('🌍 Client: http://localhost:3000 (start manually: cd client && npm run dev)');
console.log('🔌 API: http://localhost:3001');
console.log('📊 Database: PostgreSQL running in Docker');