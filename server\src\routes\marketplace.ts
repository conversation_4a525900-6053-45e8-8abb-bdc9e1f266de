import express from 'express';
import { body } from 'express-validator';
import {
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductCategories,
  createOrder,
  getUserOrders,
  getOrderById,
  updateOrderStatus
} from '../controllers/marketplaceController';
import { protect, authorize } from '../middleware/auth';

const router = express.Router();

// Product routes - Public
router.get('/products', getProducts);
router.get('/products/categories', getProductCategories);
router.get('/products/:id', getProductById);

// Product routes - Protected (sellers)
router.post('/products', protect, [
  body('name').notEmpty().withMessage('Le nom du produit est requis'),
  body('description').notEmpty().withMessage('La description est requise'),
  body('price').isFloat({ min: 0 }).withMessage('Le prix doit être un nombre positif'),
  body('category').notEmpty().withMessage('La catégorie est requise'),
  body('stock').isInt({ min: 0 }).withMessage('Le stock doit être un nombre entier positif'),
  body('images').optional().isArray().withMessage('Les images doivent être un tableau'),
  body('businessId').optional().isString().withMessage('ID d\'entreprise invalide')
], createProduct);

router.put('/products/:id', protect, [
  body('name').optional().notEmpty().withMessage('Le nom ne peut pas être vide'),
  body('description').optional().notEmpty().withMessage('La description ne peut pas être vide'),
  body('price').optional().isFloat({ min: 0 }).withMessage('Le prix doit être un nombre positif'),
  body('category').optional().notEmpty().withMessage('La catégorie ne peut pas être vide'),
  body('stock').optional().isInt({ min: 0 }).withMessage('Le stock doit être un nombre entier positif'),
  body('images').optional().isArray().withMessage('Les images doivent être un tableau'),
  body('isActive').optional().isBoolean().withMessage('Statut actif invalide')
], updateProduct);

router.delete('/products/:id', protect, deleteProduct);

// Order routes - Protected
router.post('/orders', protect, [
  body('items').isArray({ min: 1 }).withMessage('Au moins un article est requis'),
  body('items.*.productId').notEmpty().withMessage('ID de produit requis'),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantité doit être un nombre entier positif'),
  body('paymentMethod').optional().isString().withMessage('Méthode de paiement invalide'),
  body('notes').optional().isString().withMessage('Notes invalides')
], createOrder);

router.get('/orders', protect, getUserOrders);
router.get('/orders/:id', protect, getOrderById);

router.patch('/orders/:id/status', protect, [
  body('status').isIn(['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'COMPLETED', 'CANCELLED'])
    .withMessage('Statut invalide')
], updateOrderStatus);

export default router;