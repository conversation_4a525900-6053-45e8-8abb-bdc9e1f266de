export default function MessagesPage() {
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Messages</h1>
      
      <div className="grid md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <h2 className="text-xl font-semibold mb-4">Conversations</h2>
          <div className="space-y-2">
            <div className="border p-4 rounded-lg hover:bg-gray-50 cursor-pointer">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  MD
                </div>
                <div>
                  <div className="font-medium"><PERSON></div>
                  <div className="text-sm text-gray-600">Merci pour l'info sur le marché</div>
                </div>
              </div>
            </div>
            
            <div className="border p-4 rounded-lg hover:bg-gray-50 cursor-pointer">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  JM
                </div>
                <div>
                  <div className="font-medium">Jean Martin</div>
                  <div className="text-sm text-gray-600">Rdv demain à 14h ?</div>
                </div>
              </div>
            </div>
            
            <div className="border p-4 rounded-lg hover:bg-gray-50 cursor-pointer">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  SL
                </div>
                <div>
                  <div className="font-medium">Sophie Legrand</div>
                  <div className="text-sm text-gray-600">Photos du vide-grenier</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="md:col-span-2">
          <div className="border rounded-lg h-96 flex flex-col">
            <div className="border-b p-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  MD
                </div>
                <div className="font-medium">Marie Dupont</div>
              </div>
            </div>
            
            <div className="flex-1 p-4 space-y-4 overflow-y-auto">
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-lg p-3 max-w-xs">
                  Bonjour ! J'ai vu votre annonce pour le covoiturage vers Paris. Je suis intéressée.
                </div>
              </div>
              
              <div className="flex justify-end">
                <div className="bg-blue-600 text-white rounded-lg p-3 max-w-xs">
                  Parfait ! Je pars tous les matins à 8h. Ça vous convient ?
                </div>
              </div>
              
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-lg p-3 max-w-xs">
                  Oui, c'est parfait. Merci pour l'info sur le marché aussi !
                </div>
              </div>
            </div>
            
            <div className="border-t p-4">
              <div className="flex space-x-2">
                <input 
                  type="text" 
                  placeholder="Tapez votre message..." 
                  className="flex-1 border rounded px-3 py-2"
                />
                <button className="bg-blue-600 text-white px-4 py-2 rounded">Envoyer</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}