'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  Euro, 
  MapPin, 
  Truck,
  Store,
  ChevronLeft,
  CreditCard,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useApi, useApiMutation } from '@/hooks/useApi';
import api from '@/lib/api';

interface CartItem {
  id: string;
  product: {
    id: string;
    name: string;
    description: string;
    price: number;
    images: string[];
    business: {
      id: string;
      name: string;
      address: string;
    };
    deliveryAvailable: boolean;
    pickupAvailable: boolean;
    deliveryPrice?: number;
    inStock: boolean;
    stockQuantity: number;
  };
  quantity: number;
  selectedOption: 'pickup' | 'delivery';
}

export default function CartPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);

  // API hooks
  const {
    data: cartItems,
    loading,
    error,
    execute: fetchCart
  } = useApi<CartItem[]>();

  const { mutate: updateCartItem } = useApiMutation({
    onSuccess: () => fetchCart(() => api.getUserOrders()), // Refresh cart
    onError: (error) => console.error('Update cart error:', error)
  });

  const { mutate: removeCartItem } = useApiMutation({
    onSuccess: () => fetchCart(() => api.getUserOrders()), // Refresh cart
    onError: (error) => console.error('Remove cart error:', error)
  });


  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/connexion');
      return;
    }

    // Load cart items
    fetchCart(() => api.getUserOrders()); // TODO: Create specific cart endpoint
  }, [isAuthenticated, router]);

  const updateQuantity = async (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
      return;
    }

    // TODO: Implement cart update API
    try {
      await updateCartItem(() => api.put(`/cart/${itemId}`, { quantity: newQuantity }));
    } catch (error) {
      console.error('Error updating quantity:', error);
    }
  };

  const removeItem = async (itemId: string) => {
    try {
      await removeCartItem(() => api.delete(`/cart/${itemId}`));
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };

  const updateDeliveryOption = async (itemId: string, option: 'pickup' | 'delivery') => {
    try {
      await updateCartItem(() => api.put(`/cart/${itemId}`, { deliveryOption: option }));
    } catch (error) {
      console.error('Error updating delivery option:', error);
    }
  };

  const getSubtotal = () => {
    if (!cartItems) return 0;
    return cartItems.reduce((total, item) => total + (item.product.price * item.quantity), 0);
  };

  const getDeliveryTotal = () => {
    if (!cartItems) return 0;
    return cartItems.reduce((total, item) => {
      if (item.selectedOption === 'delivery' && item.product.deliveryPrice) {
        return total + item.product.deliveryPrice;
      }
      return total;
    }, 0);
  };

  const getTotal = () => {
    return getSubtotal() + getDeliveryTotal();
  };

  const proceedToCheckout = async () => {
    if (!cartItems || cartItems.length === 0) return;
    
    setIsProcessing(true);
    try {
      const orderData = {
        items: cartItems.map(item => ({
          productId: item.product.id,
          quantity: item.quantity
        }))
      };
      
      const response = await api.createOrder(orderData);
      if (response.success) {
        router.push('/marche/commande/confirmation');
      }
    } catch (error) {
      console.error('Error processing checkout:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Connexion requise</h1>
          <p className="text-gray-600 mb-6">Vous devez être connecté pour accéder à votre panier.</p>
          <Link
            href="/connexion"
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            Se connecter
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-6 w-1/3"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-20 h-20 bg-gray-200 rounded"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Navigation */}
      <div className="flex items-center space-x-2 mb-6 text-sm text-gray-600">
        <Link href="/marche" className="hover:text-primary-600 flex items-center space-x-1">
          <ChevronLeft size={16} />
          <span>Marché</span>
        </Link>
        <span>/</span>
        <span className="text-gray-900">Mon panier</span>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
          <div className="flex items-center space-x-3">
            <AlertTriangle size={24} className="text-red-600" />
            <div>
              <h3 className="text-lg font-medium text-red-900 mb-1">Erreur de chargement</h3>
              <p className="text-red-700">{error.message}</p>
              <button
                onClick={() => fetchCart(() => api.getUserOrders())}
                className="mt-3 text-red-600 hover:text-red-700 font-medium"
              >
                Réessayer
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 flex items-center space-x-3">
          <ShoppingCart size={32} />
          <span>Mon panier ({cartItems?.length || 0} article{(cartItems?.length || 0) > 1 ? 's' : ''})</span>
        </h1>

        {!cartItems || cartItems.length === 0 ? (
          <div className="text-center py-12">
            <ShoppingCart size={64} className="mx-auto text-gray-400 mb-4" />
            <h2 className="text-xl font-medium text-gray-900 mb-2">Votre panier est vide</h2>
            <p className="text-gray-600 mb-6">Découvrez nos produits locaux et ajoutez-les à votre panier.</p>
            <Link
              href="/marche"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
            >
              Découvrir le marché
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              {cartItems.map((item) => (
                <div key={item.id} className="bg-white rounded-lg shadow-sm border p-6">
                  <div className="flex items-start space-x-4">
                    {/* Product Image */}
                    <Link href={`/marche/produit/${item.product.id}`}>
                      <img
                        src={item.product.images[0]}
                        alt={item.product.name}
                        className="w-20 h-20 object-cover rounded-lg"
                      />
                    </Link>

                    <div className="flex-1">
                      {/* Product Info */}
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <Link
                            href={`/marche/produit/${item.product.id}`}
                            className="font-medium text-gray-900 hover:text-primary-600"
                          >
                            {item.product.name}
                          </Link>
                          <p className="text-sm text-gray-600">{item.product.description}</p>
                          <div className="flex items-center space-x-1 text-sm text-gray-500 mt-1">
                            <MapPin size={12} />
                            <span>{item.product.business.name}</span>
                          </div>
                        </div>
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-gray-400 hover:text-red-500 p-1"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="flex items-center border border-gray-300 rounded-lg">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="p-2 hover:bg-gray-50"
                          >
                            <Minus size={16} />
                          </button>
                          <span className="px-4 py-2 min-w-[60px] text-center">{item.quantity}</span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            className="p-2 hover:bg-gray-50"
                            disabled={item.quantity >= item.product.stockQuantity}
                          >
                            <Plus size={16} />
                          </button>
                        </div>
                        <span className="text-lg font-medium flex items-center">
                          <Euro size={18} />
                          {(item.product.price * item.quantity).toFixed(2)}
                        </span>
                      </div>

                      {/* Delivery Options */}
                      <div className="space-y-2">
                        {item.product.pickupAvailable && (
                          <label className="flex items-center space-x-3 cursor-pointer">
                            <input
                              type="radio"
                              name={`delivery-${item.id}`}
                              value="pickup"
                              checked={item.selectedOption === 'pickup'}
                              onChange={() => updateDeliveryOption(item.id, 'pickup')}
                              className="text-primary-600 focus:ring-primary-500"
                            />
                            <div className="flex items-center space-x-2 text-sm">
                              <Store size={16} />
                              <span>Retrait en magasin - Gratuit</span>
                            </div>
                          </label>
                        )}
                        {item.product.deliveryAvailable && (
                          <label className="flex items-center space-x-3 cursor-pointer">
                            <input
                              type="radio"
                              name={`delivery-${item.id}`}
                              value="delivery"
                              checked={item.selectedOption === 'delivery'}
                              onChange={() => updateDeliveryOption(item.id, 'delivery')}
                              className="text-primary-600 focus:ring-primary-500"
                            />
                            <div className="flex items-center space-x-2 text-sm">
                              <Truck size={16} />
                              <span>
                                Livraison à domicile
                                {item.product.deliveryPrice && ` - ${item.product.deliveryPrice.toFixed(2)}€`}
                              </span>
                            </div>
                          </label>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-4">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Récapitulatif</h2>
                
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between">
                    <span>Sous-total ({cartItems?.length || 0} article{(cartItems?.length || 0) > 1 ? 's' : ''})</span>
                    <span className="flex items-center">
                      <Euro size={16} />
                      {getSubtotal().toFixed(2)}
                    </span>
                  </div>
                  
                  {getDeliveryTotal() > 0 && (
                    <div className="flex justify-between">
                      <span>Frais de livraison</span>
                      <span className="flex items-center">
                        <Euro size={16} />
                        {getDeliveryTotal().toFixed(2)}
                      </span>
                    </div>
                  )}
                  
                  <hr />
                  
                  <div className="flex justify-between text-lg font-semibold">
                    <span>Total</span>
                    <span className="flex items-center">
                      <Euro size={20} />
                      {getTotal().toFixed(2)}
                    </span>
                  </div>
                </div>

                <button
                  onClick={proceedToCheckout}
                  disabled={isProcessing || !cartItems || cartItems.length === 0}
                  className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 font-medium"
                >
                  {isProcessing ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Traitement...</span>
                    </>
                  ) : (
                    <>
                      <CreditCard size={20} />
                      <span>Passer commande</span>
                    </>
                  )}
                </button>

                <p className="text-xs text-gray-500 mt-4 text-center">
                  Paiement sécurisé et données protégées
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}