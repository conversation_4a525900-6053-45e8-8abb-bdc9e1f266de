'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Search, 
  Filter, 
  X, 
  MapPin, 
  Star, 
  Clock, 
  DollarSign,
  Building,
  Package,
  Calendar,
  User,
  SlidersHorizontal,
  ChevronDown,
  Loader2
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import api from '@/lib/api';

interface SearchFilters {
  query: string;
  type: 'all' | 'businesses' | 'products' | 'events' | 'forum' | 'news' | 'jobs';
  category?: string;
  location?: string;
  priceRange?: {
    min?: number;
    max?: number;
  };
  rating?: number;
  dateRange?: {
    start?: string;
    end?: string;
  };
  tags?: string[];
  sortBy?: 'relevance' | 'date' | 'rating' | 'price' | 'name' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

interface SearchResult {
  id: string;
  type: 'business' | 'product' | 'event' | 'forum_post' | 'news' | 'job';
  title: string;
  description: string;
  image?: string;
  url: string;
  metadata: {
    category?: string;
    location?: string;
    price?: number;
    rating?: number;
    date?: string;
    author?: string;
    tags?: string[];
  };
  relevanceScore: number;
}

interface AdvancedSearchProps {
  initialQuery?: string;
  initialFilters?: Partial<SearchFilters>;
  onResults?: (results: SearchResult[]) => void;
  className?: string;
  showFilters?: boolean;
}

const defaultFilters: SearchFilters = {
  query: '',
  type: 'all',
  sortBy: 'relevance',
  sortOrder: 'desc'
};

export default function AdvancedSearch({ 
  initialQuery = '',
  initialFilters = {},
  onResults,
  className = '',
  showFilters = true
}: AdvancedSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    ...defaultFilters,
    query: initialQuery,
    ...initialFilters
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // API hooks
  const {
    data: searchResults,
    loading: searchLoading,
    execute: performSearch
  } = useApi<SearchResult[]>();

  const {
    data: categoriesData,
    execute: loadCategories
  } = useApi<string[]>();

  // Load categories on mount
  useEffect(() => {
    loadCategories(() => api.get('/search/categories'))
      .then(() => {
        if (categoriesData) {
          setCategories(categoriesData);
        }
      });
  }, []);

  // Debounced search
  const debouncedSearch = useCallback((searchFilters: SearchFilters) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      if (searchFilters.query.trim() || Object.keys(searchFilters).some(key => 
        key !== 'query' && key !== 'sortBy' && key !== 'sortOrder' && 
        searchFilters[key as keyof SearchFilters] !== undefined &&
        searchFilters[key as keyof SearchFilters] !== defaultFilters[key as keyof SearchFilters]
      )) {
        executeSearch(searchFilters);
      }
    }, 500);

    setSearchTimeout(timeout);
  }, [searchTimeout]);

  const executeSearch = async (searchFilters: SearchFilters) => {
    try {
      await performSearch(() => api.searchContent({
        q: searchFilters.query,
        type: searchFilters.type,
        category: searchFilters.category,
        location: searchFilters.location,
        minPrice: searchFilters.priceRange?.min,
        maxPrice: searchFilters.priceRange?.max,
        minRating: searchFilters.rating,
        startDate: searchFilters.dateRange?.start,
        endDate: searchFilters.dateRange?.end,
        tags: searchFilters.tags,
        sortBy: searchFilters.sortBy,
        sortOrder: searchFilters.sortOrder
      }));
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  // Update search when filters change
  useEffect(() => {
    debouncedSearch(filters);
  }, [filters, debouncedSearch]);

  // Notify parent of results
  useEffect(() => {
    if (searchResults && onResults) {
      onResults(searchResults);
    }
  }, [searchResults, onResults]);

  const updateFilter = <K extends keyof SearchFilters>(
    key: K,
    value: SearchFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters(defaultFilters);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'businesses':
        return <Building size={16} />;
      case 'products':
        return <Package size={16} />;
      case 'events':
        return <Calendar size={16} />;
      case 'forum':
        return <User size={16} />;
      case 'news':
        return <Clock size={16} />;
      case 'jobs':
        return <Building size={16} />;
      default:
        return <Search size={16} />;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={12}
            className={`${
              star <= rating 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-xs text-gray-600 ml-1">({rating.toFixed(1)})</span>
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      {/* Search Header */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-4">
          {/* Main Search Input */}
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher dans la communauté..."
              value={filters.query}
              onChange={(e) => updateFilter('query', e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
            {searchLoading && (
              <Loader2 size={20} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 animate-spin" />
            )}
          </div>

          {/* Type Selector */}
          <div className="relative">
            <select
              value={filters.type}
              onChange={(e) => updateFilter('type', e.target.value as any)}
              className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-3 pr-8 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">Tout</option>
              <option value="businesses">Entreprises</option>
              <option value="products">Produits</option>
              <option value="events">Événements</option>
              <option value="forum">Forum</option>
              <option value="news">Actualités</option>
              <option value="jobs">Emplois</option>
            </select>
            <ChevronDown size={16} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
          </div>

          {/* Advanced Filters Toggle */}
          {showFilters && (
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className={`flex items-center space-x-2 px-4 py-3 border rounded-lg transition-colors ${
                showAdvancedFilters 
                  ? 'bg-primary-50 border-primary-300 text-primary-700' 
                  : 'border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <SlidersHorizontal size={16} />
              <span>Filtres</span>
            </button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {showFilters && showAdvancedFilters && (
        <div className="p-4 bg-gray-50 border-b">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Catégorie
              </label>
              <select
                value={filters.category || ''}
                onChange={(e) => updateFilter('category', e.target.value || undefined)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Toutes les catégories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* Location Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Localisation
              </label>
              <div className="relative">
                <MapPin size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Ville, code postal..."
                  value={filters.location || ''}
                  onChange={(e) => updateFilter('location', e.target.value || undefined)}
                  className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>

            {/* Rating Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Note minimum
              </label>
              <select
                value={filters.rating || ''}
                onChange={(e) => updateFilter('rating', e.target.value ? Number(e.target.value) : undefined)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Toutes les notes</option>
                <option value="4">4+ étoiles</option>
                <option value="3">3+ étoiles</option>
                <option value="2">2+ étoiles</option>
                <option value="1">1+ étoile</option>
              </select>
            </div>

            {/* Price Range Filter */}
            {(filters.type === 'products' || filters.type === 'all') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Fourchette de prix
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.priceRange?.min || ''}
                    onChange={(e) => updateFilter('priceRange', {
                      ...filters.priceRange,
                      min: e.target.value ? Number(e.target.value) : undefined
                    })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                  <span className="text-gray-500">-</span>
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.priceRange?.max || ''}
                    onChange={(e) => updateFilter('priceRange', {
                      ...filters.priceRange,
                      max: e.target.value ? Number(e.target.value) : undefined
                    })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
            )}

            {/* Date Range Filter */}
            {(filters.type === 'events' || filters.type === 'news' || filters.type === 'all') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Période
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="date"
                    value={filters.dateRange?.start || ''}
                    onChange={(e) => updateFilter('dateRange', {
                      ...filters.dateRange,
                      start: e.target.value || undefined
                    })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                  <span className="text-gray-500">-</span>
                  <input
                    type="date"
                    value={filters.dateRange?.end || ''}
                    onChange={(e) => updateFilter('dateRange', {
                      ...filters.dateRange,
                      end: e.target.value || undefined
                    })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
            )}

            {/* Sort Options */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trier par
              </label>
              <select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split('-');
                  updateFilter('sortBy', sortBy as any);
                  updateFilter('sortOrder', sortOrder as any);
                }}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="relevance-desc">Pertinence</option>
                <option value="date-desc">Plus récent</option>
                <option value="date-asc">Plus ancien</option>
                <option value="rating-desc">Mieux notés</option>
                <option value="name-asc">Nom (A-Z)</option>
                <option value="name-desc">Nom (Z-A)</option>
                {(filters.type === 'products' || filters.type === 'all') && (
                  <>
                    <option value="price-asc">Prix croissant</option>
                    <option value="price-desc">Prix décroissant</option>
                  </>
                )}
              </select>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex justify-end space-x-2 mt-4">
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Effacer les filtres
            </button>
          </div>
        </div>
      )}

      {/* Search Results */}
      {searchResults && (
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <p className="text-sm text-gray-600">
              {searchResults.length} résultat{searchResults.length !== 1 ? 's' : ''} trouvé{searchResults.length !== 1 ? 's' : ''}
              {filters.query && ` pour "${filters.query}"`}
            </p>
          </div>

          <div className="space-y-4">
            {searchResults.map((result) => (
              <a
                key={result.id}
                href={result.url}
                className="block p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
              >
                <div className="flex items-start space-x-4">
                  {/* Result Image */}
                  {result.image && (
                    <div className="flex-shrink-0">
                      <img
                        src={result.image}
                        alt={result.title}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    </div>
                  )}

                  {/* Result Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          {getTypeIcon(result.type)}
                          <h3 className="text-lg font-semibold text-gray-900 truncate">
                            {result.title}
                          </h3>
                          {result.metadata.category && (
                            <span className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                              {result.metadata.category}
                            </span>
                          )}
                        </div>
                        
                        <p className="text-gray-600 text-sm line-clamp-2 mb-2">
                          {result.description}
                        </p>

                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          {result.metadata.location && (
                            <span className="flex items-center space-x-1">
                              <MapPin size={12} />
                              <span>{result.metadata.location}</span>
                            </span>
                          )}
                          
                          {result.metadata.rating && (
                            <div className="flex items-center space-x-1">
                              {renderStars(result.metadata.rating)}
                            </div>
                          )}
                          
                          {result.metadata.date && (
                            <span className="flex items-center space-x-1">
                              <Clock size={12} />
                              <span>{formatDate(result.metadata.date)}</span>
                            </span>
                          )}
                          
                          {result.metadata.author && (
                            <span className="flex items-center space-x-1">
                              <User size={12} />
                              <span>{result.metadata.author}</span>
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Price */}
                      {result.metadata.price !== undefined && (
                        <div className="flex-shrink-0 ml-4">
                          <span className="text-lg font-bold text-primary-600">
                            {formatPrice(result.metadata.price)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </a>
            ))}
          </div>

          {searchResults.length === 0 && filters.query && (
            <div className="text-center py-12">
              <Search size={48} className="mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucun résultat trouvé
              </h3>
              <p className="text-gray-600">
                Essayez avec d'autres mots-clés ou ajustez vos filtres.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// CSS for line-clamp
const style = `
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
`;

if (typeof document !== 'undefined' && !document.getElementById('advanced-search-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'advanced-search-styles';
  styleElement.textContent = style;
  document.head.appendChild(styleElement);
}