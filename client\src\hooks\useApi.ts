import { useState, useCallback } from 'react';
import { ApiError } from '@/lib/api';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
}

interface UseApiOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: ApiError) => void;
  showToast?: boolean;
}

export function useApi<T = any>(options: UseApiOptions = {}) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async (apiCall: () => Promise<any>) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await apiCall();
      
      if (response.success) {
        setState(prev => ({ 
          ...prev, 
          data: response.data || response,
          loading: false,
          error: null 
        }));
        
        options.onSuccess?.(response.data || response);
        return { success: true, data: response.data };
      } else {
        const error = new ApiError(
          response.message || 'Une erreur est survenue',
          0,
          'API_ERROR'
        );
        
        setState(prev => ({ 
          ...prev, 
          loading: false,
          error 
        }));
        
        options.onError?.(error);
        return { success: false, error };
      }
    } catch (error) {
      const apiError = error instanceof ApiError 
        ? error 
        : new ApiError(
            error instanceof Error ? error.message : 'Une erreur inattendue est survenue',
            0,
            'UNKNOWN_ERROR'
          );

      setState(prev => ({ 
        ...prev, 
        loading: false,
        error: apiError 
      }));
      
      options.onError?.(apiError);
      return { success: false, error: apiError };
    }
  }, [options]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  const setData = useCallback((data: T | null) => {
    setState(prev => ({ ...prev, data }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  const setError = useCallback((error: ApiError | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  return {
    ...state,
    execute,
    reset,
    setData,
    setLoading,
    setError,
  };
}

// Specialized hook for list data with pagination
export function useApiList<T = any>(options: UseApiOptions = {}) {
  const [state, setState] = useState<{
    data: T[];
    loading: boolean;
    error: ApiError | null;
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    } | null;
  }>({
    data: [],
    loading: false,
    error: null,
    pagination: null,
  });

  const execute = useCallback(async (apiCall: () => Promise<any>) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await apiCall();
      
      if (response.success) {
        setState(prev => ({ 
          ...prev, 
          data: response.data || [],
          pagination: response.pagination || null,
          loading: false,
          error: null 
        }));
        
        options.onSuccess?.(response);
        return { success: true, data: response.data, pagination: response.pagination };
      } else {
        const error = new ApiError(
          response.message || 'Une erreur est survenue',
          0,
          'API_ERROR'
        );
        
        setState(prev => ({ 
          ...prev, 
          loading: false,
          error 
        }));
        
        options.onError?.(error);
        return { success: false, error };
      }
    } catch (error) {
      const apiError = error instanceof ApiError 
        ? error 
        : new ApiError(
            error instanceof Error ? error.message : 'Une erreur inattendue est survenue',
            0,
            'UNKNOWN_ERROR'
          );

      setState(prev => ({ 
        ...prev, 
        loading: false,
        error: apiError 
      }));
      
      options.onError?.(apiError);
      return { success: false, error: apiError };
    }
  }, [options]);

  const appendData = useCallback((newData: T[]) => {
    setState(prev => ({ 
      ...prev, 
      data: [...prev.data, ...newData] 
    }));
  }, []);

  const prependData = useCallback((newData: T[]) => {
    setState(prev => ({ 
      ...prev, 
      data: [...newData, ...prev.data] 
    }));
  }, []);

  const updateItem = useCallback((id: string, updatedItem: Partial<T>) => {
    setState(prev => ({
      ...prev,
      data: prev.data.map(item => 
        (item as any).id === id ? { ...item, ...updatedItem } : item
      ),
    }));
  }, []);

  const removeItem = useCallback((id: string) => {
    setState(prev => ({
      ...prev,
      data: prev.data.filter(item => (item as any).id !== id),
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      data: [],
      loading: false,
      error: null,
      pagination: null,
    });
  }, []);

  return {
    ...state,
    execute,
    appendData,
    prependData,
    updateItem,
    removeItem,
    reset,
  };
}

// Hook for mutations (create, update, delete)
export function useApiMutation<T = any>(options: UseApiOptions = {}) {
  const [state, setState] = useState<{
    loading: boolean;
    error: ApiError | null;
    data: T | null;
  }>({
    loading: false,
    error: null,
    data: null,
  });

  const mutate = useCallback(async (apiCall: () => Promise<any>) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await apiCall();
      
      if (response.success) {
        setState(prev => ({ 
          ...prev, 
          data: response.data,
          loading: false,
          error: null 
        }));
        
        options.onSuccess?.(response.data);
        return { success: true, data: response.data };
      } else {
        const error = new ApiError(
          response.message || 'Une erreur est survenue',
          0,
          'API_ERROR'
        );
        
        setState(prev => ({ 
          ...prev, 
          loading: false,
          error 
        }));
        
        options.onError?.(error);
        return { success: false, error };
      }
    } catch (error) {
      const apiError = error instanceof ApiError 
        ? error 
        : new ApiError(
            error instanceof Error ? error.message : 'Une erreur inattendue est survenue',
            0,
            'UNKNOWN_ERROR'
          );

      setState(prev => ({ 
        ...prev, 
        loading: false,
        error: apiError 
      }));
      
      options.onError?.(apiError);
      return { success: false, error: apiError };
    }
  }, [options]);

  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      data: null,
    });
  }, []);

  return {
    ...state,
    mutate,
    reset,
  };
}

export default useApi;