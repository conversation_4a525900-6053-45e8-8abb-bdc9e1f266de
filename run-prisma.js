#!/usr/bin/env node

// Simple script to run Prisma commands with proper database URL
process.env.DATABASE_URL = "******************************************************/leclub_db";

const { execSync } = require('child_process');
const path = require('path');

// Change to server directory
process.chdir(path.join(__dirname, 'server'));

console.log('🔧 Running Prisma database push...');

try {
  // Run prisma db push
  execSync('npx prisma db push', { 
    stdio: 'inherit',
    env: { ...process.env, DATABASE_URL: "******************************************************/leclub_db" }
  });
  
  console.log('✅ Database schema pushed successfully!');
  
  console.log('🌱 Running database seed...');
  
  // Run seed script
  execSync('npm run seed', { 
    stdio: 'inherit',
    env: { ...process.env, DATABASE_URL: "******************************************************/leclub_db" }
  });
  
  console.log('✅ Database seeded successfully!');
  
} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}