'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Clock, ArrowRight } from 'lucide-react';

// Mock data - will be replaced with API calls
const mockNews = [
  {
    id: 1,
    title: "Nouveau parc communautaire inauguré",
    excerpt: "Le nouveau parc familial avec aires de jeux et espaces verts ouvre ses portes ce weekend.",
    publishedAt: "2024-01-15",
    category: "Actualités municipales",
    image: "/api/placeholder/400/200"
  },
  {
    id: 2,
    title: "Marché fermier hebdomadaire",
    excerpt: "Tous les samedis matins, découvrez les produits frais de nos producteurs locaux.",
    publishedAt: "2024-01-14",
    category: "Événements",
    image: "/api/placeholder/400/200"
  },
  {
    id: 3,
    title: "Travaux de réfection - Rue Principale",
    excerpt: "Des travaux de réfection auront lieu du 20 au 30 janvier. Circulation alternée prévue.",
    publishedAt: "2024-01-13",
    category: "Travaux publics",
    image: "/api/placeholder/400/200"
  }
];

export function FeaturedNews() {
  const t = useTranslations('news');

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {mockNews.map((article) => (
        <article key={article.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
          <div className="h-48 bg-gray-200 flex items-center justify-center">
            <span className="text-gray-500">Image à venir</span>
          </div>
          <div className="p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-primary-600 bg-primary-50 px-2 py-1 rounded">
                {article.category}
              </span>
              <div className="flex items-center text-xs text-gray-500">
                <Clock size={12} className="mr-1" />
                {new Date(article.publishedAt).toLocaleDateString('fr-FR')}
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {article.title}
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              {article.excerpt}
            </p>
            <Link 
              href={`/actualites/${article.id}`}
              className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium text-sm"
            >
              {t('readMore')}
              <ArrowRight size={16} className="ml-1" />
            </Link>
          </div>
        </article>
      ))}
    </div>
  );
}