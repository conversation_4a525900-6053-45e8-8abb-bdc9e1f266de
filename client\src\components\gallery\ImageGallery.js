'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Image as ImageIcon, 
  X, 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  Download, 
  Share2, 
  Trash2, 
  Edit, 
  Plus,
  Upload,
  Maximize2,
  Grid3X3,
  List,
  Filter,
  Search,
  Calendar,
  User,
  Tag,
  Eye,
  Heart,
  MessageCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useApi } from '@/hooks/useApi';
import api from '@/lib/api';

interface GalleryImage {
  id: string;
  url: string;
  thumbnailUrl?: string;
  title: string;
  description?: string;
  alt?: string;
  uploadedAt: string;
  uploadedBy: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  metadata: {
    size: number;
    width: number;
    height: number;
    format: string;
  };
  tags: string[];
  views: number;
  likes: number;
  comments: number;
  isLiked?: boolean;
  category?: string;
}

interface ImageGalleryProps {
  entityId?: string; // For business/product galleries
  entityType?: 'business' | 'product' | 'event' | 'general';
  images?: GalleryImage[];
  allowUpload?: boolean;
  allowEdit?: boolean;
  allowDelete?: boolean;
  maxImages?: number;
  aspectRatio?: 'square' | 'wide' | 'auto';
  layout?: 'grid' | 'masonry' | 'carousel';
  showMetadata?: boolean;
  showInteractions?: boolean;
  className?: string;
  onImagesChange?: (images: GalleryImage[]) => void;
}

export default function ImageGallery({
  entityId,
  entityType = 'general',
  images: initialImages = [],
  allowUpload = false,
  allowEdit = false,
  allowDelete = false,
  maxImages = 20,
  aspectRatio = 'auto',
  layout = 'grid',
  showMetadata = true,
  showInteractions = true,
  className = '',
  onImagesChange
}: ImageGalleryProps) {
  const { user, isAuthenticated } = useAuth();
  const [images, setImages] = useState<GalleryImage[]>(initialImages);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [lightboxIndex, setLightboxIndex] = useState<number>(-1);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'views' | 'likes'>('date');
  const [isUploading, setIsUploading] = useState(false);
  const [zoom, setZoom] = useState(1);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // API hooks
  const { loading: imagesLoading, execute: loadImages } = useApi<GalleryImage[]>();
  const { loading: uploadLoading, execute: uploadImage } = useApi();
  const { execute: deleteImage } = useApi();
  const { execute: likeImage } = useApi();

  // Load images on mount
  useEffect(() => {
    if (entityId) {
      loadImages(() => api.get(`/${entityType}/${entityId}/gallery`))
        .then((response) => {
          if (response?.data) {
            setImages(response.data);
            onImagesChange?.(response.data);
          }
        });
    }
  }, [entityId, entityType, onImagesChange]);

  // Filter and sort images
  const filteredImages = images
    .filter(image => {
      const matchesCategory = filterCategory === 'all' || image.category === filterCategory;
      const matchesSearch = !searchQuery || 
        image.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        image.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        image.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      return matchesCategory && matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title);
        case 'views':
          return b.views - a.views;
        case 'likes':
          return b.likes - a.likes;
        default:
          return new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime();
      }
    });

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    await handleFileUpload(files);
  }, []);

  const handleFileUpload = async (files: File[]) => {
    if (!allowUpload || !isAuthenticated) return;

    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) return;

    if (images.length + imageFiles.length > maxImages) {
      alert(`Vous ne pouvez télécharger que ${maxImages - images.length} image(s) supplémentaire(s).`);
      return;
    }

    setIsUploading(true);

    try {
      for (const file of imageFiles) {
        const response = await uploadImage(() => api.uploadFile(file, 'gallery'));
        if (response?.data) {
          const newImage: GalleryImage = {
            id: response.data.id,
            url: response.data.url,
            thumbnailUrl: response.data.thumbnailUrl,
            title: file.name.split('.')[0],
            uploadedAt: new Date().toISOString(),
            uploadedBy: {
              id: user!.id,
              firstName: user!.firstName,
              lastName: user!.lastName,
              avatar: user!.avatar
            },
            metadata: {
              size: file.size,
              width: response.data.width || 0,
              height: response.data.height || 0,
              format: file.type
            },
            tags: [],
            views: 0,
            likes: 0,
            comments: 0,
            isLiked: false
          };
          
          setImages(prev => [newImage, ...prev]);
        }
      }
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Erreur lors du téléchargement des images.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async (imageId: string) => {
    if (!allowDelete || !confirm('Êtes-vous sûr de vouloir supprimer cette image ?')) return;

    try {
      await deleteImage(() => api.delete(`/gallery/${imageId}`));
      setImages(prev => prev.filter(img => img.id !== imageId));
      setSelectedImage(null);
      setLightboxIndex(-1);
    } catch (error) {
      console.error('Delete failed:', error);
      alert('Erreur lors de la suppression de l\'image.');
    }
  };

  const handleLike = async (imageId: string) => {
    if (!isAuthenticated) return;

    try {
      await likeImage(() => api.post(`/gallery/${imageId}/like`));
      setImages(prev => prev.map(img => 
        img.id === imageId 
          ? { ...img, isLiked: !img.isLiked, likes: img.isLiked ? img.likes - 1 : img.likes + 1 }
          : img
      ));
    } catch (error) {
      console.error('Like failed:', error);
    }
  };

  const openLightbox = (image: GalleryImage, index: number) => {
    setSelectedImage(image);
    setLightboxIndex(index);
    setZoom(1);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
    setLightboxIndex(-1);
    setZoom(1);
  };

  const navigateLightbox = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? (lightboxIndex - 1 + filteredImages.length) % filteredImages.length
      : (lightboxIndex + 1) % filteredImages.length;
    
    setLightboxIndex(newIndex);
    setSelectedImage(filteredImages[newIndex]);
    setZoom(1);
  };

  const downloadImage = (image: GalleryImage) => {
    const link = document.createElement('a');
    link.href = image.url;
    link.download = `${image.title}.${image.metadata.format.split('/')[1]}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const shareImage = async (image: GalleryImage) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: image.title,
          text: image.description,
          url: image.url
        });
      } catch (error) {
        console.error('Share failed:', error);
      }
    } else {
      // Fallback: copy URL to clipboard
      navigator.clipboard.writeText(image.url);
      alert('Lien copié dans le presse-papiers');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getImageAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square';
      case 'wide':
        return 'aspect-video';
      default:
        return '';
    }
  };

  return (
    <div className={`image-gallery ${className}`}>
      {/* Gallery Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Galerie ({filteredImages.length})
          </h3>
          
          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
            >
              <Grid3X3 size={16} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
            >
              <List size={16} />
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="date">Plus récent</option>
            <option value="name">Nom</option>
            <option value="views">Vues</option>
            <option value="likes">Likes</option>
          </select>

          {/* Upload Button */}
          {allowUpload && isAuthenticated && (
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading || images.length >= maxImages}
              className="flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Upload size={16} />
              <span>Ajouter</span>
            </button>
          )}
        </div>
      </div>

      {/* Upload Zone */}
      {allowUpload && isAuthenticated && (
        <div
          ref={dropZoneRef}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          className="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-6 text-center hover:border-primary-400 transition-colors"
        >
          {isUploading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
              <span>Téléchargement en cours...</span>
            </div>
          ) : (
            <div>
              <ImageIcon size={32} className="mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">
                Glissez-déposez vos images ici ou{' '}
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  cliquez pour sélectionner
                </button>
              </p>
              <p className="text-sm text-gray-500 mt-2">
                JPG, PNG, GIF jusqu'à 10MB • {images.length}/{maxImages} images
              </p>
            </div>
          )}
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={(e) => e.target.files && handleFileUpload(Array.from(e.target.files))}
        className="hidden"
      />

      {/* Gallery Content */}
      {imagesLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className={`bg-gray-300 rounded-lg ${getImageAspectRatioClass()} mb-3`}></div>
              <div className="h-4 bg-gray-300 rounded mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      ) : filteredImages.length === 0 ? (
        <div className="text-center py-12">
          <ImageIcon size={48} className="mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery ? 'Aucune image trouvée' : 'Aucune image'}
          </h3>
          <p className="text-gray-600">
            {searchQuery 
              ? 'Essayez avec d\'autres mots-clés'
              : allowUpload && isAuthenticated
              ? 'Ajoutez vos premières images'
              : 'Aucune image disponible pour le moment'
            }
          </p>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? `grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4`
            : 'space-y-4'
        }>
          {filteredImages.map((image, index) => (
            <div key={image.id} className={
              viewMode === 'grid' 
                ? 'group cursor-pointer'
                : 'flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow'
            }>
              {/* Image */}
              <div className={`relative overflow-hidden rounded-lg ${
                viewMode === 'grid' 
                  ? `${getImageAspectRatioClass()} group-hover:shadow-lg transition-shadow`
                  : 'w-24 h-24 flex-shrink-0'
              }`}>
                <img
                  src={image.thumbnailUrl || image.url}
                  alt={image.alt || image.title}
                  className="w-full h-full object-cover"
                  onClick={() => openLightbox(image, index)}
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-opacity flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        openLightbox(image, index);
                      }}
                      className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-colors"
                    >
                      <Maximize2 size={16} />
                    </button>
                    {showInteractions && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleLike(image.id);
                        }}
                        className={`p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-colors ${
                          image.isLiked ? 'text-red-500' : ''
                        }`}
                      >
                        <Heart size={16} fill={image.isLiked ? 'currentColor' : 'none'} />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Image Info */}
              <div className={viewMode === 'grid' ? 'mt-3' : 'flex-1 min-w-0'}>
                <h4 className="font-medium text-gray-900 truncate">
                  {image.title}
                </h4>
                
                {showMetadata && (
                  <div className="mt-1 space-y-1">
                    <div className="flex items-center text-xs text-gray-500">
                      <User size={12} className="mr-1" />
                      <span>{image.uploadedBy.firstName} {image.uploadedBy.lastName}</span>
                      <span className="mx-2">•</span>
                      <Calendar size={12} className="mr-1" />
                      <span>{formatDate(image.uploadedAt)}</span>
                    </div>
                    
                    {showInteractions && (
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="flex items-center">
                          <Eye size={12} className="mr-1" />
                          {image.views}
                        </span>
                        <span className="flex items-center">
                          <Heart size={12} className="mr-1" />
                          {image.likes}
                        </span>
                        <span className="flex items-center">
                          <MessageCircle size={12} className="mr-1" />
                          {image.comments}
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* Tags */}
                {image.tags.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {image.tags.slice(0, 3).map((tag, tagIndex) => (
                      <span key={tagIndex} className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                        #{tag}
                      </span>
                    ))}
                    {image.tags.length > 3 && (
                      <span className="text-xs text-gray-500">+{image.tags.length - 3}</span>
                    )}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className={`${viewMode === 'grid' ? 'hidden' : 'flex'} items-center space-x-2`}>
                <button
                  onClick={() => shareImage(image)}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Share2 size={16} />
                </button>
                <button
                  onClick={() => downloadImage(image)}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Download size={16} />
                </button>
                {allowDelete && (image.uploadedBy.id === user?.id || user?.role === 'ADMIN') && (
                  <button
                    onClick={() => handleDelete(image.id)}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Lightbox */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-7xl max-h-full">
            {/* Close Button */}
            <button
              onClick={closeLightbox}
              className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
            >
              <X size={24} />
            </button>

            {/* Navigation */}
            {filteredImages.length > 1 && (
              <>
                <button
                  onClick={() => navigateLightbox('prev')}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
                >
                  <ChevronLeft size={24} />
                </button>
                <button
                  onClick={() => navigateLightbox('next')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
                >
                  <ChevronRight size={24} />
                </button>
              </>
            )}

            {/* Zoom Controls */}
            <div className="absolute top-4 left-4 z-10 flex items-center space-x-2">
              <button
                onClick={() => setZoom(Math.max(0.5, zoom - 0.25))}
                className="p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
              >
                <ZoomOut size={20} />
              </button>
              <span className="text-white text-sm bg-black bg-opacity-50 px-2 py-1 rounded">
                {Math.round(zoom * 100)}%
              </span>
              <button
                onClick={() => setZoom(Math.min(3, zoom + 0.25))}
                className="p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
              >
                <ZoomIn size={20} />
              </button>
            </div>

            {/* Image */}
            <div className="relative overflow-hidden">
              <img
                src={selectedImage.url}
                alt={selectedImage.alt || selectedImage.title}
                className="max-w-full max-h-[80vh] object-contain transition-transform duration-200"
                style={{ transform: `scale(${zoom})` }}
              />
            </div>

            {/* Image Info */}
            <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 rounded-lg p-4 text-white">
              <h3 className="text-lg font-semibold mb-2">{selectedImage.title}</h3>
              {selectedImage.description && (
                <p className="text-sm opacity-90 mb-2">{selectedImage.description}</p>
              )}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm">
                  <span>{selectedImage.uploadedBy.firstName} {selectedImage.uploadedBy.lastName}</span>
                  <span>{formatDate(selectedImage.uploadedAt)}</span>
                  <span>{selectedImage.metadata.width}×{selectedImage.metadata.height}</span>
                  <span>{formatFileSize(selectedImage.metadata.size)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => shareImage(selectedImage)}
                    className="p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                  >
                    <Share2 size={16} />
                  </button>
                  <button
                    onClick={() => downloadImage(selectedImage)}
                    className="p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                  >
                    <Download size={16} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// CSS for smooth transitions
const style = `
.image-gallery img {
  transition: transform 0.3s ease;
}

.image-gallery .group:hover img {
  transform: scale(1.05);
}
`;

if (typeof document !== 'undefined' && !document.getElementById('image-gallery-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'image-gallery-styles';
  styleElement.textContent = style;
  document.head.appendChild(styleElement);
}