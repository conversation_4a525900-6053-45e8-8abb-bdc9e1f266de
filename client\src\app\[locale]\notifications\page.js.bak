'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { 
  Bell, 
  Settings, 
  Check, 
  Trash2, 
  Filter,
  Smartphone,
  Mail,
  Globe,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  MessageSquare,
  Package,
  Calendar,
  Briefcase
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { useApi } from '@/hooks/useApi';
import api from '@/lib/api';
import { NotificationData } from '@/lib/socket';

interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  emergencyAlerts: boolean;
  forumUpdates: boolean;
  marketplaceUpdates: boolean;
  eventReminders: boolean;
  newsUpdates: boolean;
  jobAlerts: boolean;
  marketingEmails: boolean;
}

export default function NotificationsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { 
    notifications, 
    unreadCount, 
    markAsRead, 
    markAllAsRead, 
    removeNotification,
    isConnected
  } = useNotifications();
  
  const [activeTab, setActiveTab] = useState('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [showSettings, setShowSettings] = useState(false);

  // API hooks for preferences
  const {
    data: preferences,
    loading: preferencesLoading,
    execute: loadPreferences
  } = useApi<NotificationPreferences>();

  const {
    loading: updateLoading,
    execute: updatePreferences
  } = useApi();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/connexion');
      return;
    }

    loadPreferences(() => api.getNotificationPreferences());
  }, [isAuthenticated, router]);

  // Filter notifications based on selected type
  const filteredNotifications = notifications.filter(notification => {
    if (selectedType === 'all') return true;
    return notification.type === selectedType;
  });

  // Group notifications by read status
  const unreadNotifications = filteredNotifications.filter(n => !n.read);
  const readNotifications = filteredNotifications.filter(n => n.read);

  const displayedNotifications = activeTab === 'unread' 
    ? unreadNotifications 
    : activeTab === 'read' 
    ? readNotifications 
    : filteredNotifications;

  const handlePreferenceChange = async (key: keyof NotificationPreferences, value: boolean) => {
    if (!preferences) return;

    const updatedPreferences = {
      ...preferences,
      [key]: value
    };

    try {
      await updatePreferences(() => api.updateNotificationPreferences(updatedPreferences));
      // Update local state optimistically
      loadPreferences(() => api.getNotificationPreferences());
    } catch (error) {
      console.error('Failed to update preferences:', error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'emergency':
        return <AlertTriangle size={20} className="text-red-600" />;
      case 'error':
        return <XCircle size={20} className="text-red-600" />;
      case 'warning':
        return <AlertTriangle size={20} className="text-orange-600" />;
      case 'success':
        return <CheckCircle size={20} className="text-green-600" />;
      case 'info':
        return <Info size={20} className="text-blue-600" />;
      default:
        return <Bell size={20} className="text-gray-600" />;
    }
  };

  const getTypeIcon = (metadata?: Record<string, any>) => {
    if (metadata?.source === 'forum') return <MessageSquare size={16} className="text-blue-600" />;
    if (metadata?.source === 'marketplace') return <Package size={16} className="text-green-600" />;
    if (metadata?.source === 'events') return <Calendar size={16} className="text-purple-600" />;
    if (metadata?.source === 'jobs') return <Briefcase size={16} className="text-orange-600" />;
    return null;
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) return 'Il y a moins d\'1h';
    if (diffHours < 24) return `Il y a ${diffHours}h`;
    if (diffDays < 7) return `Il y a ${diffDays}j`;
    
    return date.toLocaleDateString('fr-FR', { 
      day: 'numeric', 
      month: 'short',
      year: diffDays > 365 ? 'numeric' : undefined
    });
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Connexion requise</h1>
          <p className="text-gray-600">Vous devez être connecté pour accéder à vos notifications.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Notifications</h1>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-orange-500'}`} />
                <span>{isConnected ? 'Temps réel activé' : 'Déconnecté'}</span>
              </span>
              {unreadCount > 0 && (
                <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                  {unreadCount} non lue{unreadCount > 1 ? 's' : ''}
                </span>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-medium"
              >
                <Check size={18} />
                <span>Tout marquer lu</span>
              </button>
            )}
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg transition-colors"
            >
              <Settings size={18} />
              <span>Paramètres</span>
            </button>
          </div>
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <div className="bg-white rounded-lg shadow-sm border mb-8 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Préférences de notifications</h3>
            
            {preferencesLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : preferences ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                    <Bell size={16} />
                    <span>Notifications générales</span>
                  </h4>
                  
                  {[
                    { key: 'emergencyAlerts', label: 'Alertes d\'urgence', icon: AlertTriangle, required: true },
                    { key: 'newsUpdates', label: 'Actualités locales', icon: Globe },
                    { key: 'eventReminders', label: 'Rappels d\'événements', icon: Calendar },
                  ].map(({ key, label, icon: Icon, required }) => (
                    <label key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Icon size={16} className="text-gray-600" />
                        <div>
                          <span className="text-sm font-medium text-gray-900">{label}</span>
                          {required && <span className="text-xs text-red-600 ml-1">(obligatoire)</span>}
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={preferences[key as keyof NotificationPreferences] as boolean}
                        onChange={(e) => handlePreferenceChange(key as keyof NotificationPreferences, e.target.checked)}
                        disabled={required || updateLoading}
                        className="h-4 w-4 text-primary-600 rounded border-gray-300 focus:ring-primary-500"
                      />
                    </label>
                  ))}
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                    <MessageSquare size={16} />
                    <span>Activités communautaires</span>
                  </h4>
                  
                  {[
                    { key: 'forumUpdates', label: 'Nouveaux posts forum', icon: MessageSquare },
                    { key: 'marketplaceUpdates', label: 'Mises à jour marché', icon: Package },
                    { key: 'jobAlerts', label: 'Offres d\'emploi', icon: Briefcase },
                  ].map(({ key, label, icon: Icon }) => (
                    <label key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Icon size={16} className="text-gray-600" />
                        <span className="text-sm font-medium text-gray-900">{label}</span>
                      </div>
                      <input
                        type="checkbox"
                        checked={preferences[key as keyof NotificationPreferences] as boolean}
                        onChange={(e) => handlePreferenceChange(key as keyof NotificationPreferences, e.target.checked)}
                        disabled={updateLoading}
                        className="h-4 w-4 text-primary-600 rounded border-gray-300 focus:ring-primary-500"
                      />
                    </label>
                  ))}
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                    <Smartphone size={16} />
                    <span>Canaux de notification</span>
                  </h4>
                  
                  {[
                    { key: 'pushNotifications', label: 'Notifications push', icon: Smartphone },
                    { key: 'emailNotifications', label: 'Notifications email', icon: Mail },
                    { key: 'marketingEmails', label: 'Emails marketing', icon: Mail },
                  ].map(({ key, label, icon: Icon }) => (
                    <label key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Icon size={16} className="text-gray-600" />
                        <span className="text-sm font-medium text-gray-900">{label}</span>
                      </div>
                      <input
                        type="checkbox"
                        checked={preferences[key as keyof NotificationPreferences] as boolean}
                        onChange={(e) => handlePreferenceChange(key as keyof NotificationPreferences, e.target.checked)}
                        disabled={updateLoading}
                        className="h-4 w-4 text-primary-600 rounded border-gray-300 focus:ring-primary-500"
                      />
                    </label>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-gray-600">Impossible de charger les préférences.</p>
            )}
          </div>
        )}

        {/* Filter and Tabs */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="border-b border-gray-200">
            <div className="flex items-center justify-between p-4">
              <div className="flex space-x-6">
                {[
                  { key: 'all', label: 'Toutes', count: notifications.length },
                  { key: 'unread', label: 'Non lues', count: unreadNotifications.length },
                  { key: 'read', label: 'Lues', count: readNotifications.length }
                ].map(({ key, label, count }) => (
                  <button
                    key={key}
                    onClick={() => setActiveTab(key)}
                    className={`pb-4 border-b-2 font-medium text-sm ${
                      activeTab === key
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {label} ({count})
                  </button>
                ))}
              </div>

              {/* Type Filter */}
              <div className="flex items-center space-x-2">
                <Filter size={16} className="text-gray-400" />
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">Tous types</option>
                  <option value="emergency">Urgences</option>
                  <option value="info">Informations</option>
                  <option value="success">Succès</option>
                  <option value="warning">Avertissements</option>
                  <option value="error">Erreurs</option>
                </select>
              </div>
            </div>
          </div>

          {/* Notifications List */}
          <div className="divide-y divide-gray-100">
            {displayedNotifications.length === 0 ? (
              <div className="p-12 text-center text-gray-500">
                <Bell size={48} className="mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucune notification
                </h3>
                <p>
                  {activeTab === 'unread' 
                    ? 'Toutes vos notifications ont été lues'
                    : selectedType !== 'all'
                    ? `Aucune notification de type "${selectedType}"`
                    : 'Vous n\'avez aucune notification pour le moment'
                  }
                </p>
              </div>
            ) : (
              displayedNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-gray-50 transition-colors ${
                    !notification.read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    {/* Icon */}
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="text-sm font-semibold text-gray-900">
                              {notification.title}
                            </h3>
                            {getTypeIcon(notification.metadata)}
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full" />
                            )}
                          </div>
                          <p className="text-sm text-gray-600 leading-relaxed">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-3">
                            <span className="text-xs text-gray-500">
                              {formatTime(notification.timestamp)}
                            </span>
                            {notification.actionUrl && (
                              <a
                                href={notification.actionUrl}
                                className="text-xs text-primary-600 hover:text-primary-700 font-medium"
                              >
                                {notification.actionText || 'Voir plus'} →
                              </a>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2 ml-4">
                          {!notification.read && (
                            <button
                              onClick={() => markAsRead(notification.id)}
                              className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                              title="Marquer comme lu"
                            >
                              <Check size={16} />
                            </button>
                          )}
                          <button
                            onClick={() => removeNotification(notification.id)}
                            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                            title="Supprimer"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}