import { io, Socket } from 'socket.io-client';

interface ServerToClientEvents {
  notification: (data: NotificationData) => void;
  'notification:emergency': (data: EmergencyNotification) => void;
  'forum:new_post': (data: ForumPostNotification) => void;
  'forum:new_comment': (data: ForumCommentNotification) => void;
  'marketplace:order_update': (data: OrderUpdateNotification) => void;
  'community:announcement': (data: AnnouncementNotification) => void;
  'user:message': (data: MessageNotification) => void;
}

interface ClientToServerEvents {
  'join:community': (communityId: string) => void;
  'leave:community': (communityId: string) => void;
  'join:forum': (forumId: string) => void;
  'leave:forum': (forumId: string) => void;
  'mark:notification_read': (notificationId: string) => void;
  'mark:all_read': () => void;
}

export interface NotificationData {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'emergency';
  title: string;
  message: string;
  timestamp: string;
  userId: string;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
  metadata?: Record<string, any>;
}

export interface EmergencyNotification extends NotificationData {
  type: 'emergency';
  priority: 'critical' | 'high';
  area?: string;
  expiresAt?: string;
}

export interface ForumPostNotification extends NotificationData {
  postId: string;
  authorName: string;
  categoryName: string;
}

export interface ForumCommentNotification extends NotificationData {
  postId: string;
  commentId: string;
  authorName: string;
  postTitle: string;
}

export interface OrderUpdateNotification extends NotificationData {
  orderId: string;
  status: 'confirmed' | 'prepared' | 'delivered' | 'cancelled';
  businessName: string;
}

export interface AnnouncementNotification extends NotificationData {
  announcementId: string;
  category: 'news' | 'event' | 'maintenance' | 'general';
}

export interface MessageNotification extends NotificationData {
  conversationId: string;
  senderName: string;
  messagePreview: string;
}

class SocketService {
  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(userId: string, token: string): Socket<ServerToClientEvents, ClientToServerEvents> {
    if (this.socket?.connected) {
      return this.socket;
    }

    const serverUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001';

    this.socket = io(serverUrl, {
      auth: {
        token,
        userId
      },
      transports: ['websocket', 'polling'],
      timeout: 5000,
      retries: 3
    });

    this.setupEventListeners();

    return this.socket;
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('📡 Connected to notification server');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('📡 Disconnected from notification server:', reason);
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, reconnect manually
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('📡 Connection error:', error);
      this.handleReconnect();
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`📡 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
      
      setTimeout(() => {
        this.socket?.connect();
      }, delay);
    } else {
      console.error('📡 Max reconnection attempts reached');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.reconnectAttempts = 0;
  }

  // Room management
  joinCommunity(communityId: string) {
    this.socket?.emit('join:community', communityId);
  }

  leaveCommunity(communityId: string) {
    this.socket?.emit('leave:community', communityId);
  }

  joinForum(forumId: string) {
    this.socket?.emit('join:forum', forumId);
  }

  leaveForum(forumId: string) {
    this.socket?.emit('leave:forum', forumId);
  }

  // Notification management
  markNotificationRead(notificationId: string) {
    this.socket?.emit('mark:notification_read', notificationId);
  }

  markAllNotificationsRead() {
    this.socket?.emit('mark:all_read');
  }

  // Event listeners
  onNotification(callback: (data: NotificationData) => void) {
    this.socket?.on('notification', callback);
  }

  onEmergencyNotification(callback: (data: EmergencyNotification) => void) {
    this.socket?.on('notification:emergency', callback);
  }

  onForumPost(callback: (data: ForumPostNotification) => void) {
    this.socket?.on('forum:new_post', callback);
  }

  onForumComment(callback: (data: ForumCommentNotification) => void) {
    this.socket?.on('forum:new_comment', callback);
  }

  onOrderUpdate(callback: (data: OrderUpdateNotification) => void) {
    this.socket?.on('marketplace:order_update', callback);
  }

  onAnnouncement(callback: (data: AnnouncementNotification) => void) {
    this.socket?.on('community:announcement', callback);
  }

  onMessage(callback: (data: MessageNotification) => void) {
    this.socket?.on('user:message', callback);
  }

  // Remove listeners
  offNotification(callback?: (data: NotificationData) => void) {
    this.socket?.off('notification', callback);
  }

  offEmergencyNotification(callback?: (data: EmergencyNotification) => void) {
    this.socket?.off('notification:emergency', callback);
  }

  offForumPost(callback?: (data: ForumPostNotification) => void) {
    this.socket?.off('forum:new_post', callback);
  }

  offForumComment(callback?: (data: ForumCommentNotification) => void) {
    this.socket?.off('forum:new_comment', callback);
  }

  offOrderUpdate(callback?: (data: OrderUpdateNotification) => void) {
    this.socket?.off('marketplace:order_update', callback);
  }

  offAnnouncement(callback?: (data: AnnouncementNotification) => void) {
    this.socket?.off('community:announcement', callback);
  }

  offMessage(callback?: (data: MessageNotification) => void) {
    this.socket?.off('user:message', callback);
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getSocket(): Socket<ServerToClientEvents, ClientToServerEvents> | null {
    return this.socket;
  }
}

// Singleton instance
export const socketService = new SocketService();
export default socketService;