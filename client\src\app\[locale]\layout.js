import React from 'react';

export default function LocaleLayout({ children, params }) {
  const { locale } = params;
  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-blue-600 text-white p-4">
        <div className="container mx-auto">
          <h1 className="text-xl font-bold mb-2">LeClub - Communauté Locale</h1>
          <nav className="flex flex-wrap gap-4 text-sm">
            <a href="/fr" className="hover:underline">Accueil</a>
            <a href="/fr/actualites" className="hover:underline">Actualités</a>
            <a href="/fr/evenements" className="hover:underline">Événements</a>
            <a href="/fr/forum" className="hover:underline">Forum</a>
            <a href="/fr/marche" className="hover:underline">Marché</a>
            <a href="/fr/entreprises" className="hover:underline">Entreprises</a>
            <a href="/fr/emplois" className="hover:underline">Emplois</a>
            <a href="/fr/parametres" className="hover:underline">Paramètres</a>
            <a href="/fr/recherche" className="hover:underline">Recherche</a>
            <a href="/fr/messages" className="hover:underline">Messages</a>
          </nav>
        </div>
      </header>
      <main className="flex-1 p-8 container mx-auto">
        {children}
      </main>
      <footer className="bg-gray-100 p-4 text-center text-sm text-gray-600">
        © 2024 LeClub - Votre communauté locale
      </footer>
    </div>
  );
}