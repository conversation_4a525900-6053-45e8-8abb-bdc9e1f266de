services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: leclub-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: leclub_db
      POSTGRES_USER: leclub_user
      POSTGRES_PASSWORD: leclub_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - leclub-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U leclub_user -d leclub_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Express.js API Server
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: leclub-server
    restart: unless-stopped
    environment:
      - DATABASE_URL=******************************************************/leclub_db
      - JWT_SECRET=leclub-super-secret-jwt-key-change-this-in-production-2024
      - JWT_EXPIRES_IN=7d
      - PORT=3001
      - NODE_ENV=development
      - CORS_ORIGIN=http://localhost:3000
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - leclub-network
    volumes:
      - ./server/uploads:/app/uploads
    command: sh -c "npx prisma migrate deploy && npm start"

  # Next.js Client
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: leclub-client
    restart: unless-stopped
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3001/api
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - NEXT_PUBLIC_DEFAULT_LOCALE=fr
    ports:
      - "3000:3000"
    depends_on:
      - server
    networks:
      - leclub-network

  # pgAdmin (optionnel - interface d'administration)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: leclub-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - leclub-network
    profiles:
      - admin  # Démarré seulement avec --profile admin

volumes:
  postgres_data:
    driver: local

networks:
  leclub-network:
    driver: bridge