'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { 
  ChevronLeft, 
  Star, 
  MapPin, 
  Euro, 
  ShoppingCart, 
  Heart, 
  Share2, 
  Truck,
  Store,
  Clock,
  Shield,
  Plus,
  Minus,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useApi, useApiMutation } from '@/hooks/useApi';
import api from '@/lib/api';

interface Product {
  id: string;
  name: string;
  description: string;
  longDescription: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  inStock: boolean;
  stockQuantity: number;
  rating: number;
  reviewCount: number;
  business: {
    id: string;
    name: string;
    isVerified: boolean;
    address: string;
    phone?: string;
    email?: string;
    openingHours: string;
  };
  isNew: boolean;
  isOnSale: boolean;
  deliveryAvailable: boolean;
  pickupAvailable: boolean;
  deliveryPrice?: number;
  deliveryTime?: string;
  ingredients?: string[];
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

interface Review {
  id: string;
  rating: number;
  comment: string;
  author: {
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  createdAt: string;
}

export default function ProductPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);

  // API hooks
  const {
    data: product,
    loading,
    error,
    execute: fetchProduct
  } = useApi<Product>();

  const {
    data: reviews,
    execute: fetchReviews
  } = useApi<Review[]>();

  const { mutate: addToCartMutation, loading: addingToCart } = useApiMutation({
    onSuccess: () => {
      // TODO: Show success message
      console.log('Added to cart successfully');
    },
    onError: (error) => {
      console.error('Add to cart error:', error);
    }
  });


  useEffect(() => {
    if (params.id) {
      // Load product and reviews in parallel
      Promise.all([
        fetchProduct(() => api.getProduct(params.id as string)),
        fetchReviews(() => api.get(`/marketplace/products/${params.id}/reviews`))
      ]);
    }
  }, [params.id]);

  const addToCart = async () => {
    if (!product || !isAuthenticated) return;
    
    try {
      await addToCartMutation(() => 
        api.post('/cart', {
          productId: product.id,
          quantity
        })
      );
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };

  const toggleWishlist = async () => {
    if (!product || !isAuthenticated) return;
    
    try {
      if (isWishlisted) {
        await api.delete(`/wishlist/${product.id}`);
      } else {
        await api.post('/wishlist', { productId: product.id });
      }
      setIsWishlisted(!isWishlisted);
    } catch (error) {
      console.error('Error toggling wishlist:', error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-6 w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="h-96 bg-gray-200 rounded-lg"></div>
            <div>
              <div className="h-8 bg-gray-200 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded mb-4 w-3/4"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <AlertTriangle size={24} className="text-red-600" />
            <div>
              <h3 className="text-lg font-medium text-red-900 mb-1">Erreur de chargement</h3>
              <p className="text-red-700">{error.message}</p>
              <button
                onClick={() => fetchProduct(() => api.getProduct(params.id as string))}
                className="mt-3 text-red-600 hover:text-red-700 font-medium"
              >
                Réessayer
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Produit introuvable</h1>
          <Link href="/marche" className="text-primary-600 hover:text-primary-700">
            Retour au marché
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Navigation */}
      <div className="flex items-center space-x-2 mb-6 text-sm text-gray-600">
        <Link href="/marche" className="hover:text-primary-600 flex items-center space-x-1">
          <ChevronLeft size={16} />
          <span>Marché</span>
        </Link>
        <span>/</span>
        <span className="text-gray-900">{product.name}</span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Images */}
        <div className="space-y-4">
          <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={product.images[selectedImageIndex]}
              alt={product.name}
              className="w-full h-full object-cover"
            />
          </div>
          {product.images.length > 1 && (
            <div className="flex space-x-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`w-20 h-20 rounded-lg overflow-hidden ${
                    selectedImageIndex === index ? 'ring-2 ring-primary-500' : ''
                  }`}
                >
                  <img src={image} alt={`${product.name} ${index + 1}`} className="w-full h-full object-cover" />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <div className="flex items-start justify-between mb-2">
              <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleWishlist}
                  className={`p-2 rounded-full ${
                    isWishlisted ? 'text-red-500' : 'text-gray-400 hover:text-red-500'
                  }`}
                >
                  <Heart size={24} className={isWishlisted ? 'fill-current' : ''} />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full">
                  <Share2 size={24} />
                </button>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex items-center space-x-1">
                <Star size={20} className="text-yellow-400 fill-current" />
                <span className="font-medium">{product.rating}</span>
                <span className="text-gray-500">({product.reviewCount} avis)</span>
              </div>
              <span className="text-gray-300">•</span>
              <span className="text-sm text-gray-600">{product.category}</span>
            </div>

            <p className="text-gray-600 mb-6">{product.description}</p>
          </div>

          {/* Price */}
          <div className="flex items-center space-x-3">
            <span className="text-3xl font-bold text-gray-900 flex items-center">
              <Euro size={28} />
              {product.price.toFixed(2)}
            </span>
            {product.originalPrice && (
              <span className="text-xl text-gray-500 line-through flex items-center">
                <Euro size={20} />
                {product.originalPrice.toFixed(2)}
              </span>
            )}
            {product.isOnSale && (
              <span className="bg-red-500 text-white px-2 py-1 rounded text-sm">Promo</span>
            )}
          </div>

          {/* Business Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-2">
              <MapPin size={16} className="text-gray-400" />
              <Link
                href={`/entreprises/${product.business.id}`}
                className="font-medium text-gray-900 hover:text-primary-600"
              >
                {product.business.name}
              </Link>
              {product.business.isVerified && (
                <Shield size={16} className="text-green-600" />
              )}
            </div>
            <p className="text-sm text-gray-600 mb-2">{product.business.address}</p>
            <div className="flex items-center space-x-1 text-sm text-gray-600">
              <Clock size={14} />
              <span>{product.business.openingHours}</span>
            </div>
          </div>

          {/* Availability */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              {product.inStock ? (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-green-600 font-medium">En stock ({product.stockQuantity} disponible{product.stockQuantity > 1 ? 's' : ''})</span>
                </>
              ) : (
                <>
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-red-600 font-medium">Rupture de stock</span>
                </>
              )}
            </div>
            
            <div className="space-y-2">
              {product.pickupAvailable && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Store size={16} />
                  <span>Retrait en magasin disponible</span>
                </div>
              )}
              {product.deliveryAvailable && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Truck size={16} />
                  <span>
                    Livraison disponible
                    {product.deliveryPrice && ` (${product.deliveryPrice}€)`}
                    {product.deliveryTime && ` - ${product.deliveryTime}`}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Add to Cart */}
          {isAuthenticated && product.inStock && (
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <label className="text-sm font-medium text-gray-700">Quantité:</label>
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 hover:bg-gray-50"
                  >
                    <Minus size={16} />
                  </button>
                  <span className="px-4 py-2 min-w-[60px] text-center">{quantity}</span>
                  <button
                    onClick={() => setQuantity(Math.min(product.stockQuantity, quantity + 1))}
                    className="p-2 hover:bg-gray-50"
                  >
                    <Plus size={16} />
                  </button>
                </div>
              </div>
              
              <button
                onClick={addToCart}
                disabled={addingToCart}
                className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 font-medium"
              >
                {addingToCart ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Ajout en cours...</span>
                  </>
                ) : (
                  <>
                    <ShoppingCart size={20} />
                    <span>Ajouter au panier - {(product.price * quantity).toFixed(2)}€</span>
                  </>
                )}
              </button>
            </div>
          )}

          {!isAuthenticated && (
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-600 mb-3">Connectez-vous pour commander</p>
              <Link
                href="/connexion"
                className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
              >
                Se connecter
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <nav className="flex space-x-8">
          <button className="py-2 px-1 border-b-2 border-primary-500 text-primary-600 font-medium">
            Description
          </button>
          <button className="py-2 px-1 text-gray-500 hover:text-gray-700">
            Avis ({product.reviewCount})
          </button>
          {product.ingredients && (
            <button className="py-2 px-1 text-gray-500 hover:text-gray-700">
              Ingrédients
            </button>
          )}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="py-8">
        <div className="prose max-w-none">
          <div className="whitespace-pre-wrap text-gray-900">{product.longDescription}</div>
        </div>

        {/* Reviews */}
        <div className="mt-12">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Avis clients</h3>
          <div className="space-y-6">
            {reviews?.map((review) => (
              <div key={review.id} className="border-b pb-6">
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-medium text-sm">
                      {review.author.firstName[0]}{review.author.lastName[0]}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="font-medium text-gray-900">
                        {review.author.firstName} {review.author.lastName}
                      </span>
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            size={14}
                            className={i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-500">{formatDate(review.createdAt)}</span>
                    </div>
                    <p className="text-gray-700">{review.comment}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}