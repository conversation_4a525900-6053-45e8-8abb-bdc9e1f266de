# Guide d'Installation - LeClub

Ce guide vous aidera à installer et configurer la plateforme LeClub sur votre machine locale.

## Prérequis

- Node.js 18+ et npm
- PostgreSQL 13+
- Git

## Installation

### 1. <PERSON><PERSON><PERSON> le projet

```bash
git clone [votre-repo-url]
cd leclub
```

### 2. Installer les dépendances

```bash
# Installer les dépendances principales
npm install

# Installer les dépendances du client
cd client && npm install && cd ..

# Installer les dépendances du serveur
cd server && npm install && cd ..
```

### 3. Configuration de la base de données

1. Créer une base de données PostgreSQL :
```sql
CREATE DATABASE leclub_db;
```

2. Copier les fichiers d'environnement :
```bash
cp server/.env.example server/.env
cp client/.env.example client/.env.local
```

3. Modifier `server/.env` avec vos informations :
```env
DATABASE_URL="postgresql://username:password@localhost:5432/leclub_db"
JWT_SECRET="votre-clé-jwt-secrète"
```

### 4. Migration de la base de données

```bash
cd server
npx prisma migrate dev --name init
npx prisma generate
```

### 5. Données de test (optionnel)

```bash
cd server
npm run seed
```

## Démarrage

### Développement

Pour démarrer les deux applications simultanément :
```bash
npm run dev
```

Ou individuellement :
```bash
# Client (port 3000)
npm run dev:client

# Serveur (port 3001)
npm run dev:server
```

### Accès à l'application

- **Frontend** : http://localhost:3000
- **API Backend** : http://localhost:3001
- **Prisma Studio** : `cd server && npx prisma studio`

## Configuration avancée

### Variables d'environnement serveur

Consultez `server/.env.example` pour toutes les options disponibles :
- Configuration SMTP pour les emails
- Clés Stripe pour les paiements
- Limites de taux et sécurité

### Variables d'environnement client

Consultez `client/.env.example` pour :
- URL de l'API
- Clés publiques Stripe
- Configuration des cartes (Google Maps)

## Vérification de l'installation

1. Vérifiez que l'API fonctionne : http://localhost:3001/health
2. Vérifiez que le frontend se charge : http://localhost:3000
3. Testez la création d'un compte utilisateur

## Dépannage

### Problèmes courants

1. **Erreur de connexion à la base de données**
   - Vérifiez que PostgreSQL est démarré
   - Vérifiez l'URL de connexion dans `.env`

2. **Port déjà utilisé**
   - Changez les ports dans les fichiers de configuration
   - Ou arrêtez les processus utilisant les ports 3000/3001

3. **Erreurs de migration Prisma**
   - Supprimez le dossier `prisma/migrations`
   - Relancez `npx prisma migrate dev --name init`

### Support

Pour toute question ou problème, consultez :
- Documentation Prisma : https://www.prisma.io/docs
- Documentation Next.js : https://nextjs.org/docs
- Issues du projet : [lien vers vos issues GitHub]

## Production

Pour un déploiement en production, consultez le guide de déploiement dans `docs/DEPLOYMENT.md`.