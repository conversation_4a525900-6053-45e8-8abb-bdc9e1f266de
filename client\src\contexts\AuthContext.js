'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User } from '@/lib/types';
import api, { ApiError } from '@/lib/api';

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string; error?: ApiError }>;
  register: (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => Promise<{ success: boolean; message?: string; error?: ApiError }>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  updateProfile: (userData: any) => Promise<{ success: boolean; message?: string; error?: ApiError }>;
  getToken: () => string | null;
}

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'LOGOUT' };

const initialState: AuthState = {
  user: null,
  isLoading: true,
  isAuthenticated: false,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
      };
    case 'LOGOUT':
      return {
        user: null,
        isLoading: false,
        isAuthenticated: false,
      };
    default:
      return state;
  }
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state on mount
  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('auth_token');
      const userData = localStorage.getItem('user');

      if (token && userData) {
        try {
          // Verify token is still valid by fetching profile
          const response = await api.getProfile();
          if (response.success && response.data) {
            dispatch({ type: 'SET_USER', payload: response.data });
          } else {
            // Token invalid, clear storage
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user');
            dispatch({ type: 'SET_USER', payload: null });
          }
        } catch (error) {
          // Token invalid or network error, clear storage
          console.warn('Auth verification failed:', error);
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          dispatch({ type: 'SET_USER', payload: null });
        }
      } else {
        dispatch({ type: 'SET_USER', payload: null });
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const response = await api.login(email, password);
      
      if (response.success && response.data) {
        dispatch({ type: 'SET_USER', payload: response.data.user });
        return { success: true };
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
        return { success: false, message: response.message || 'Erreur de connexion' };
      }
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      
      if (error instanceof ApiError) {
        return { 
          success: false, 
          message: error.message,
          error 
        };
      }
      
      return { 
        success: false, 
        message: 'Erreur de connexion inattendue',
        error: error as ApiError
      };
    }
  };

  const register = async (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const response = await api.register(userData);
      
      if (response.success) {
        // Auto-login after successful registration
        return await login(userData.email, userData.password);
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
        return { success: false, message: response.message || 'Erreur d\'inscription' };
      }
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      
      if (error instanceof ApiError) {
        return { 
          success: false, 
          message: error.message,
          error 
        };
      }
      
      return { 
        success: false, 
        message: 'Erreur d\'inscription inattendue',
        error: error as ApiError
      };
    }
  };

  const logout = () => {
    api.logout();
    dispatch({ type: 'LOGOUT' });
  };

  const refreshUser = async () => {
    try {
      const response = await api.getProfile();
      if (response.success && response.data) {
        dispatch({ type: 'SET_USER', payload: response.data });
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  };

  const updateProfile = async (userData: any) => {
    try {
      const response = await api.updateProfile(userData);
      
      if (response.success && response.data) {
        // Ensure the response data is a valid User object
        const updatedUser = response.data as User;
        dispatch({ type: 'SET_USER', payload: updatedUser });
        return { success: true, message: 'Profil mis à jour avec succès' };
      } else {
        return { success: false, message: response.message || 'Erreur lors de la mise à jour' };
      }
    } catch (error) {
      if (error instanceof ApiError) {
        return { 
          success: false, 
          message: error.message,
          error 
        };
      }
      
      return { 
        success: false, 
        message: 'Erreur lors de la mise à jour du profil',
        error: error as ApiError
      };
    }
  };

  const getToken = (): string | null => {
    return api.getAuthToken();
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshUser,
    updateProfile,
    getToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;