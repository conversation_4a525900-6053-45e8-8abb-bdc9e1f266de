{"name": "community-hub-marketplace", "version": "1.0.0", "private": true, "description": "Une place publique numérique combinant communication communautaire et fonctionnalités de marché local", "scripts": {"dev": "docker-compose up --build", "dev:legacy": "npm run docker:up && concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "build": "docker-compose build", "build:server": "cd server && npm run build", "build:client": "cd client && npm run build", "start": "docker-compose up -d", "stop": "docker-compose down", "restart": "docker-compose restart", "logs": "docker-compose logs -f", "logs:server": "docker-compose logs -f server", "logs:client": "docker-compose logs -f client", "test": "npm run test:server && npm run test:client", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "lint": "npm run lint:server && npm run lint:client", "lint:server": "cd server && npm run lint", "lint:client": "cd client && npm run lint", "docker:up": "docker-compose up -d postgres", "docker:down": "docker-compose down", "docker:reset": "docker-compose down -v && docker-compose up --build", "docker:admin": "docker-compose --profile admin up -d pgadmin", "docker:clean": "docker-compose down -v --remove-orphans && docker system prune -f", "db:migrate": "docker-compose exec server npx prisma migrate dev", "db:reset": "docker-compose exec server npx prisma migrate reset", "db:studio": "docker-compose exec server npx prisma studio", "db:seed": "docker-compose exec server npm run seed"}, "workspaces": ["client", "server", "shared"], "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["communauté", "marché-local", "commerce-local", "engagement-civique", "français"], "author": "", "license": "MIT"}