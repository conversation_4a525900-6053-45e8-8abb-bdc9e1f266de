export default function ParametresPage() {
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Paramètres</h1>
      
      <div className="grid gap-6">
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Profil utilisateur</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Prénom</label>
              <input type="text" className="w-full border rounded px-3 py-2" defaultValue="Marie" />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Nom</label>
              <input type="text" className="w-full border rounded px-3 py-2" defaultValue="Dupont" />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Email</label>
              <input type="email" className="w-full border rounded px-3 py-2" defaultValue="<EMAIL>" />
            </div>
          </div>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Notifications</h2>
          <div className="space-y-3">
            <label className="flex items-center">
              <input type="checkbox" className="mr-3" defaultChecked />
              <span>Notifications par email</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" className="mr-3" defaultChecked />
              <span>Alertes d'urgence</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" className="mr-3" />
              <span>Actualités de la communauté</span>
            </label>
          </div>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Confidentialité</h2>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-2">Visibilité du profil</label>
              <select className="w-full border rounded px-3 py-2">
                <option>Public</option>
                <option selected>Membres uniquement</option>
                <option>Privé</option>
              </select>
            </div>
            <label className="flex items-center">
              <input type="checkbox" className="mr-3" />
              <span>Afficher mon email publiquement</span>
            </label>
          </div>
        </div>
      </div>
      
      <div className="mt-6">
        <button className="bg-blue-600 text-white px-6 py-2 rounded">Sauvegarder</button>
      </div>
    </div>
  );
}