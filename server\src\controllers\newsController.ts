import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { AuthRequest } from '../middleware/auth';

const prisma = new PrismaClient();

// @desc Get all news
// @route GET /api/news
// @access Public
export const getNews = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10, category, search } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const where: any = {
      isPublished: true
    };

    if (category) {
      where.category = category;
    }

    if (search) {
      where.OR = [
        { title: { contains: search as string, mode: 'insensitive' } },
        { content: { contains: search as string, mode: 'insensitive' } }
      ];
    }

    // Get news with pagination
    const [news, total] = await Promise.all([
      prisma.news.findMany({
        where,
        skip,
        take: Number(limit),
        orderBy: { publishedAt: 'desc' },
        include: {
          author: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        }
      }),
      prisma.news.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        news,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des actualités'
    });
  }
};

// @desc Get single news article
// @route GET /api/news/:id
// @access Public
export const getNewsById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const newsArticle = await prisma.news.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    });

    if (!newsArticle) {
      return res.status(404).json({
        success: false,
        message: 'Article non trouvé'
      });
    }

    if (!newsArticle.isPublished) {
      return res.status(404).json({
        success: false,
        message: 'Article non disponible'
      });
    }

    res.json({
      success: true,
      data: newsArticle
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'article'
    });
  }
};

// @desc Create news article
// @route POST /api/news
// @access Private (Admin only)
export const createNews = async (req: AuthRequest, res: Response) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { title, content, excerpt, image, category, isPublished = false } = req.body;

    const newsArticle = await prisma.news.create({
      data: {
        title,
        content,
        excerpt,
        image,
        category,
        isPublished,
        publishedAt: isPublished ? new Date() : null,
        authorId: req.user.id
      },
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Article créé avec succès',
      data: newsArticle
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de l\'article'
    });
  }
};

// @desc Update news article
// @route PUT /api/news/:id
// @access Private (Admin only)
export const updateNews = async (req: AuthRequest, res: Response) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { title, content, excerpt, image, category, isPublished } = req.body;

    // Check if article exists
    const existingArticle = await prisma.news.findUnique({
      where: { id }
    });

    if (!existingArticle) {
      return res.status(404).json({
        success: false,
        message: 'Article non trouvé'
      });
    }

    const updateData: any = {};
    if (title) updateData.title = title;
    if (content) updateData.content = content;
    if (excerpt) updateData.excerpt = excerpt;
    if (image) updateData.image = image;
    if (category) updateData.category = category;
    if (typeof isPublished === 'boolean') {
      updateData.isPublished = isPublished;
      if (isPublished && !existingArticle.publishedAt) {
        updateData.publishedAt = new Date();
      }
    }

    const updatedArticle = await prisma.news.update({
      where: { id },
      data: updateData,
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Article mis à jour avec succès',
      data: updatedArticle
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de l\'article'
    });
  }
};

// @desc Delete news article
// @route DELETE /api/news/:id
// @access Private (Admin only)
export const deleteNews = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Check if article exists
    const existingArticle = await prisma.news.findUnique({
      where: { id }
    });

    if (!existingArticle) {
      return res.status(404).json({
        success: false,
        message: 'Article non trouvé'
      });
    }

    await prisma.news.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Article supprimé avec succès'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'article'
    });
  }
};