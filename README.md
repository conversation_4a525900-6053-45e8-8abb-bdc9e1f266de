# Plateforme Communautaire & Marché Local

Une place publique numérique qui combine les fonctionnalités de communication communautaire avec un marché local, conçue pour renforcer les liens communautaires et soutenir les entreprises locales.

## Fonctionnalités

### Communication Communautaire
- **Actualités & Annonces**: Flux central pour les nouvelles officielles du village et les mises à jour
- **Calendrier d'Événements**: Calendrier interactif pour les événements et activités locaux
- **Forums de Discussion**: Discussions communautaires modérées et engagement civique
- **Alertes d'Urgence**: Système de notifications push pour les informations communautaires urgentes

### Marché Local & Répertoire d'Entreprises
- **Répertoire d'Entreprises**: Profils riches pour les entreprises locales avec avis et cartes
- **Marché Local**: Plateforme e-commerce pour les artisans et commerçants locaux
- **Offres d'Emploi**: Opportunités d'emploi local
- **Paiements Sécurisés**: Traitement des paiements intégré avec retrait/livraison locale

## Stack Technique

- **Frontend**: Next.js avec TypeScript et Tailwind CSS
- **Backend**: Node.js avec Express et TypeScript
- **Base de données**: PostgreSQL avec Prisma ORM
- **Authentification**: NextAuth.js
- **Paiements**: Stripe
- **Temps réel**: Socket.io
- **Déploiement**: Conteneurisation Docker

## Structure du Projet

```
├── client/          # Application frontend Next.js
├── server/          # API backend Express.js
├── shared/          # Types et utilitaires partagés
├── docs/            # Documentation du projet
└── docker/          # Fichiers de configuration Docker
```

## Démarrage

1. Cloner le dépôt
2. Installer les dépendances: `npm install`
3. Configurer les variables d'environnement (voir les fichiers .env.example)
4. Démarrer les serveurs de développement: `npm run dev`

## Développement

- `npm run dev` - Démarrer le client et le serveur en mode développement
- `npm run build` - Construire les deux applications pour la production
- `npm run test` - Exécuter tous les tests
- `npm run lint` - Exécuter le linting pour les deux applications

## Contribution

Veuillez lire nos directives de contribution et notre code de conduite avant de soumettre des pull requests.

## Licence

Licence MIT - voir le fichier LICENSE pour plus de détails.