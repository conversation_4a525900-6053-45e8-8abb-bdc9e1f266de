// User types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  bio?: string;
  phone?: string;
  address?: string;
  role: 'RESIDENT' | 'BUSINESS_OWNER' | 'ADMIN';
  isVerified: boolean;
  isActive: boolean;
  communityId?: string;
  createdAt: string;
  updatedAt: string;
}

// Event types
export interface Event {
  id: string;
  title: string;
  description: string;
  location: string;
  date: string;
  endDate?: string;
  image?: string;
  category: string;
  maxAttendees?: number;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  organizer: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  attendees?: EventAttendee[];
  _count: {
    attendees: number;
  };
}

export interface EventAttendee {
  id: string;
  status: 'INTERESTED' | 'ATTENDING' | 'NOT_ATTENDING';
  joinedAt: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
}

// Forum types
export interface ForumCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  posts?: ForumPost[];
  _count: {
    posts: number;
  };
}

export interface ForumPost {
  id: string;
  title: string;
  content: string;
  isPinned: boolean;
  isLocked: boolean;
  views: number;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    bio?: string;
  };
  category: {
    id: string;
    name: string;
    color?: string;
  };
  comments?: ForumComment[];
  _count: {
    comments: number;
  };
}

export interface ForumComment {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
}

// Business types
export interface Business {
  id: string;
  name: string;
  description?: string;
  category: string;
  address: string;
  phone?: string;
  email?: string;
  website?: string;
  hours?: any; // JSON object
  logo?: string;
  images: string[];
  isVerified: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  owner: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  products?: Product[];
  reviews?: Review[];
  _count: {
    reviews: number;
    products: number;
  };
  averageRating: number;
}

export interface Review {
  id: string;
  rating: number; // 1-5
  comment?: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
}

// Product types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: string; // Decimal as string
  category: string;
  images: string[];
  stock: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  seller: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  business?: {
    id: string;
    name: string;
    isVerified: boolean;
  };
  reviews?: Review[];
  _count: {
    reviews: number;
  };
  averageRating: number;
}

// Order types
export interface Order {
  id: string;
  total: string; // Decimal as string
  status: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'COMPLETED' | 'CANCELLED';
  paymentMethod?: string;
  paymentId?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  buyer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
  };
  items: OrderItem[];
}

export interface OrderItem {
  id: string;
  quantity: number;
  price: string; // Decimal as string
  product: {
    id: string;
    name: string;
    images: string[];
    seller?: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
}

// News types (if needed)
export interface News {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  image?: string;
  category: 'OFFICIAL' | 'COMMUNITY' | 'EVENTS' | 'BUSINESS' | 'EMERGENCY';
  isPublished: boolean;
  isPinned: boolean;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
}

// API Response types
export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ApiError {
  success: false;
  message: string;
  errors?: Array<{
    type: string;
    msg: string;
    path: string;
    location: string;
  }>;
}