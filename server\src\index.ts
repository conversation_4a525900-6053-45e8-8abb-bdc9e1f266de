import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import rateLimit from 'express-rate-limit';

// Import routes
import authRoutes from './routes/auth';
import newsRoutes from './routes/news';
import eventsRoutes from './routes/events';
import forumRoutes from './routes/forum';
import businessRoutes from './routes/business';
import marketplaceRoutes from './routes/marketplace';
import jobsRoutes from './routes/jobs';
import userRoutes from './routes/user';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { notFound } from './middleware/notFound';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Trop de requêtes depuis cette IP, veuillez réessayer plus tard.'
});

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:3000",
  credentials: true
}));

// General middleware
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(limiter);

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'LeClub API is running',
    timestamp: new Date().toISOString()
  });
});

// API documentation endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'LeClub API - Endpoints disponibles',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth - Authentification (register, login, profile)',
      news: '/api/news - Actualités et annonces',
      events: '/api/events - Événements communautaires',
      forum: '/api/forum - Forum de discussion',
      business: '/api/business - Répertoire des entreprises',
      marketplace: '/api/marketplace - Marché local',
      jobs: '/api/jobs - Offres d\'emploi',
      user: '/api/user - Gestion utilisateur'
    },
    health: '/health - Status du serveur'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/news', newsRoutes);
app.use('/api/events', eventsRoutes);
app.use('/api/forum', forumRoutes);
app.use('/api/business', businessRoutes);
app.use('/api/marketplace', marketplaceRoutes);
app.use('/api/jobs', jobsRoutes);
app.use('/api/user', userRoutes);

// Socket.IO for real-time features
io.on('connection', (socket) => {
  console.log('Utilisateur connecté:', socket.id);
  
  // Join room for emergency alerts
  socket.on('join-community', (communityId: string) => {
    socket.join(`community-${communityId}`);
  });
  
  // Handle forum real-time updates
  socket.on('join-forum', (forumId: string) => {
    socket.join(`forum-${forumId}`);
  });
  
  socket.on('disconnect', () => {
    console.log('Utilisateur déconnecté:', socket.id);
  });
});

// Error handling middleware (must be last)
app.use(notFound);
app.use(errorHandler);

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Serveur LeClub démarré sur le port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌍 CORS origin: ${process.env.CORS_ORIGIN || 'http://localhost:3000'}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Arrêt du serveur...');
  server.close(() => {
    console.log('✅ Serveur arrêté proprement');
    process.exit(0);
  });
});

export { io };