import express from 'express';
import { body } from 'express-validator';
import {
  getCategories,
  getPosts,
  getPostById,
  createPost,
  updatePost,
  deletePost,
  addComment,
  updateComment,
  deleteComment,
  togglePinPost,
  toggleLockPost
} from '../controllers/forumController';
import { protect, authorize } from '../middleware/auth';

const router = express.Router();

// Public routes
router.get('/categories', getCategories);
router.get('/posts', getPosts);
router.get('/posts/:id', getPostById);

// Protected routes - Authenticated users can create posts and comments
router.post('/posts', protect, [
  body('title').notEmpty().withMessage('Le titre est requis'),
  body('content').notEmpty().withMessage('Le contenu est requis'),
  body('categoryId').notEmpty().withMessage('La catégorie est requise')
], createPost);

router.put('/posts/:id', protect, [
  body('title').optional().notEmpty().withMessage('Le titre ne peut pas être vide'),
  body('content').optional().notEmpty().withMessage('Le contenu ne peut pas être vide')
], updatePost);

router.delete('/posts/:id', protect, deletePost);

// Comment routes
router.post('/posts/:id/comments', protect, [
  body('content').notEmpty().withMessage('Le contenu du commentaire est requis')
], addComment);

router.put('/comments/:commentId', protect, [
  body('content').notEmpty().withMessage('Le contenu du commentaire est requis')
], updateComment);

router.delete('/comments/:commentId', protect, deleteComment);

// Admin routes - Pin/Lock posts
router.patch('/posts/:id/pin', protect, authorize('ADMIN'), togglePinPost);
router.patch('/posts/:id/lock', protect, authorize('ADMIN'), toggleLockPost);

export default router;