#!/bin/bash

echo "🚀 Starting LeClub development environment..."

# Start PostgreSQL
echo "📦 Starting PostgreSQL..."
docker-compose up -d postgres

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 5

# Start the server using Docker network
echo "🔧 Starting API server..."
docker run --rm -d \
  --name leclub-server-dev \
  --network leclub_leclub-network \
  -v "$(pwd)/server":/app \
  -w /app \
  -p 3001:3001 \
  -e DATABASE_URL="******************************************************/leclub_db" \
  -e JWT_SECRET="leclub-super-secret-jwt-key-change-this-in-production-2024" \
  -e JWT_EXPIRES_IN="7d" \
  -e PORT=3001 \
  -e NODE_ENV="development" \
  -e CORS_ORIGIN="http://localhost:3000" \
  node:18-alpine \
  sh -c "npm install && npm run dev"

# Start the client locally
echo "🌐 Starting Next.js client..."
cd client && npm run dev &

echo "✅ Development environment started!"
echo "🌍 Client: http://localhost:3000"
echo "🔌 API: http://localhost:3001"
echo "📊 Database: PostgreSQL running in Docker"

# Keep script running and show logs
echo "📝 API Server logs:"
docker logs -f leclub-server-dev