'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Calendar, MapPin, Users, ArrowRight } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Event } from '@/lib/types';
import api from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';

export function UpcomingEvents() {
  const t = useTranslations('events');
  const { isAuthenticated } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const response = await api.getEvents({
          upcoming: true,
          limit: 6
        });
        
        if (response.success && response.data) {
          setEvents(response.data);
        } else {
          setError('Erreur lors du chargement des événements');
        }
      } catch (err) {
        console.error('Error fetching events:', err);
        setError('Erreur lors du chargement des événements');
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const handleRSVP = async (eventId: string, status: 'ATTENDING' | 'INTERESTED') => {
    if (!isAuthenticated) {
      // Redirect to login or show login modal
      alert('Veuillez vous connecter pour répondre à l\'invitation');
      return;
    }

    try {
      const response = await api.rsvpEvent(eventId, status);
      if (response.success) {
        // Refresh events to update attendee count
        const eventsResponse = await api.getEvents({
          upcoming: true,
          limit: 6
        });
        if (eventsResponse.success && eventsResponse.data) {
          setEvents(eventsResponse.data);
        }
      }
    } catch (err) {
      console.error('Error updating RSVP:', err);
      alert('Erreur lors de la mise à jour de votre réponse');
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="mt-4 text-primary-600 hover:text-primary-700"
        >
          Réessayer
        </button>
      </div>
    );
  }

  if (events.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Aucun événement à venir pour le moment.</p>
        <Link 
          href="/evenements" 
          className="mt-4 inline-block text-primary-600 hover:text-primary-700"
        >
          Voir tous les événements
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {events.map((event) => {
        const eventDate = new Date(event.date);
        const eventTime = eventDate.toLocaleTimeString('fr-FR', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
        
        return (
          <div key={event.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <span className="text-xs font-medium text-secondary-600 bg-secondary-50 px-2 py-1 rounded">
                {event.category}
              </span>
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-900">
                  {eventDate.toLocaleDateString('fr-FR', { 
                    day: 'numeric', 
                    month: 'short' 
                  })}
                </div>
                <div className="text-xs text-gray-500">
                  {eventTime}
                </div>
              </div>
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {event.title}
            </h3>
            
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {event.description}
            </p>
            
            <div className="space-y-2 mb-4">
              <div className="flex items-center text-sm text-gray-500">
                <MapPin size={14} className="mr-2" />
                {event.location}
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <Users size={14} className="mr-2" />
                {event._count.attendees} participant{event._count.attendees !== 1 ? 's' : ''}
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                <button 
                  onClick={() => handleRSVP(event.id, 'ATTENDING')}
                  className="text-primary-600 hover:text-primary-700 font-medium text-sm px-2 py-1 rounded hover:bg-primary-50 transition-colors"
                >
                  Participer
                </button>
                <button 
                  onClick={() => handleRSVP(event.id, 'INTERESTED')}
                  className="text-gray-600 hover:text-gray-700 font-medium text-sm px-2 py-1 rounded hover:bg-gray-50 transition-colors"
                >
                  Intéressé
                </button>
              </div>
              <Link 
                href={`/evenements/${event.id}`}
                className="inline-flex items-center text-gray-600 hover:text-gray-700 text-sm"
              >
                Détails
                <ArrowRight size={14} className="ml-1" />
              </Link>
            </div>
          </div>
        );
      })}
    </div>
  );
}