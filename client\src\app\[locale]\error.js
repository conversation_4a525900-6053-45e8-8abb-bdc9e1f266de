'use client';

export default function Error({ error, reset }) {
  return (
    <div className="text-center py-8">
      <h2 className="text-xl font-bold text-red-600 mb-4">Oops! Une erreur s'est produite</h2>
      <p className="text-gray-600 mb-4">
        Nous rencontrons des difficultés techniques. Veuillez réessayer.
      </p>
      <button
        onClick={reset}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
      >
        R<PERSON>sayer
      </button>
    </div>
  );
}