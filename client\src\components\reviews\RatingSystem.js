'use client';

import React, { useState, useEffect } from 'react';
import { 
  Star, 
  ThumbsUp, 
  ThumbsDown, 
  Flag, 
  Reply, 
  Edit, 
  Trash2, 
  MoreH<PERSON>zontal,
  Filter,
  SortAsc,
  User,
  Calendar,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useApi } from '@/hooks/useApi';
import { useFormValidation } from '@/hooks/useFormValidation';
import { FormTextarea, FormButton } from '@/components/forms/FormField';
import { reviewValidation } from '@/lib/validation';
import api from '@/lib/api';

interface Review {
  id: string;
  rating: number;
  title: string;
  comment: string;
  createdAt: string;
  updatedAt?: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    isVerified: boolean;
    reviewCount: number;
    averageRating: number;
  };
  likes: number;
  dislikes: number;
  isLiked?: boolean;
  isDisliked?: boolean;
  replies: ReviewReply[];
  isEdited: boolean;
  isPinned: boolean;
  status: 'published' | 'pending' | 'flagged' | 'hidden';
  metadata: {
    helpful: number;
    verified: boolean;
    purchaseVerified?: boolean;
  };
}

interface ReviewReply {
  id: string;
  comment: string;
  createdAt: string;
  author: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    role: 'USER' | 'BUSINESS_OWNER' | 'ADMIN';
  };
  likes: number;
  isLiked?: boolean;
}

interface RatingBreakdown {
  average: number;
  total: number;
  distribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

interface RatingSystemProps {
  entityId: string;
  entityType: 'business' | 'product' | 'event';
  allowReviews?: boolean;
  allowReplies?: boolean;
  showBreakdown?: boolean;
  showFilters?: boolean;
  className?: string;
}

export default function RatingSystem({
  entityId,
  entityType,
  allowReviews = true,
  allowReplies = true,
  showBreakdown = true,
  showFilters = true,
  className = ''
}: RatingSystemProps) {
  const { user, isAuthenticated } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [breakdown, setBreakdown] = useState<RatingBreakdown | null>(null);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [editingReview, setEditingReview] = useState<Review | null>(null);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [filterRating, setFilterRating] = useState<number | null>(null);
  const [sortBy, setSortBy] = useState<'recent' | 'oldest' | 'helpful' | 'rating'>('recent');
  const [showOnlyVerified, setShowOnlyVerified] = useState(false);

  // API hooks
  const { 
    data: reviewsData, 
    loading: reviewsLoading, 
    execute: loadReviews 
  } = useApi<{ reviews: Review[]; breakdown: RatingBreakdown }>();

  const { loading: submitLoading, execute: submitReview } = useApi();
  const { execute: likeReview } = useApi();
  const { execute: reportReview } = useApi();

  // Form validation
  const {
    formData,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    reset,
    setError
  } = useFormValidation({
    validationRules: reviewValidation,
    initialData: {
      title: '',
      comment: '',
      rating: 0
    },
    validateOnBlur: true
  });

  // Load reviews
  useEffect(() => {
    loadReviewsData();
  }, [entityId, entityType, filterRating, sortBy, showOnlyVerified]);

  const loadReviewsData = async () => {
    try {
      const params = new URLSearchParams();
      if (filterRating) params.set('rating', filterRating.toString());
      params.set('sort', sortBy);
      if (showOnlyVerified) params.set('verified', 'true');

      await loadReviews(() => api.get(`/${entityType}/${entityId}/reviews?${params.toString()}`));
    } catch (error) {
      console.error('Failed to load reviews:', error);
    }
  };

  useEffect(() => {
    if (reviewsData) {
      setReviews(reviewsData.reviews);
      setBreakdown(reviewsData.breakdown);
    }
  }, [reviewsData]);

  const handleRatingChange = (newRating: number) => {
    setRating(newRating);
    handleChange({ target: { name: 'rating', value: newRating } } as any);
  };

  const onSubmitReview = async (data: any) => {
    try {
      const reviewData = {
        ...data,
        rating,
        entityId,
        entityType
      };

      if (editingReview) {
        await submitReview(() => api.put(`/reviews/${editingReview.id}`, reviewData));
      } else {
        await submitReview(() => api.post('/reviews', reviewData));
      }

      // Reload reviews
      await loadReviewsData();
      
      // Reset form
      reset();
      setRating(0);
      setShowReviewForm(false);
      setEditingReview(null);
    } catch (error: any) {
      setError('submit', error.message || 'Erreur lors de la soumission');
    }
  };

  const handleLike = async (reviewId: string, isReply = false) => {
    if (!isAuthenticated) return;

    try {
      await likeReview(() => api.post(`/reviews/${reviewId}/${isReply ? 'reply-' : ''}like`));
      await loadReviewsData();
    } catch (error) {
      console.error('Like failed:', error);
    }
  };

  const handleReport = async (reviewId: string, reason: string) => {
    if (!isAuthenticated) return;

    try {
      await reportReview(() => api.post(`/reviews/${reviewId}/report`, { reason }));
      alert('Signalement envoyé');
    } catch (error) {
      console.error('Report failed:', error);
    }
  };

  const startEdit = (review: Review) => {
    setEditingReview(review);
    setRating(review.rating);
    reset({
      title: review.title,
      comment: review.comment,
      rating: review.rating
    });
    setShowReviewForm(true);
  };

  const cancelEdit = () => {
    setEditingReview(null);
    setShowReviewForm(false);
    setRating(0);
    reset();
  };

  const renderStars = (rating: number, interactive = false, size = 20) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            disabled={!interactive}
            onClick={() => interactive && handleRatingChange(star)}
            onMouseEnter={() => interactive && setHoverRating(star)}
            onMouseLeave={() => interactive && setHoverRating(0)}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
          >
            <Star
              size={size}
              className={`${
                star <= (interactive ? (hoverRating || rating) : rating)
                  ? 'text-yellow-400 fill-current' 
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  const renderRatingBreakdown = () => {
    if (!breakdown || !showBreakdown) return null;

    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-6">
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 mb-2">
              {breakdown.average.toFixed(1)}
            </div>
            <div className="flex items-center justify-center mb-2">
              {renderStars(breakdown.average)}
            </div>
            <div className="text-sm text-gray-600">
              {breakdown.total} avis
            </div>
          </div>
          
          <div className="flex-1 max-w-md ml-8">
            {[5, 4, 3, 2, 1].map((stars) => {
              const count = breakdown.distribution[stars as keyof typeof breakdown.distribution];
              const percentage = breakdown.total > 0 ? (count / breakdown.total) * 100 : 0;
              
              return (
                <div key={stars} className="flex items-center space-x-3 mb-2">
                  <span className="text-sm font-medium w-8">{stars}★</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 w-12 text-right">{count}</span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const renderFilters = () => {
    if (!showFilters) return null;

    return (
      <div className="flex flex-wrap items-center gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2">
          <Filter size={16} className="text-gray-500" />
          <span className="text-sm font-medium text-gray-700">Filtres:</span>
        </div>
        
        <select
          value={filterRating || ''}
          onChange={(e) => setFilterRating(e.target.value ? Number(e.target.value) : null)}
          className="border border-gray-300 rounded px-3 py-1 text-sm"
        >
          <option value="">Toutes les notes</option>
          <option value="5">5 étoiles</option>
          <option value="4">4 étoiles</option>
          <option value="3">3 étoiles</option>
          <option value="2">2 étoiles</option>
          <option value="1">1 étoile</option>
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="border border-gray-300 rounded px-3 py-1 text-sm"
        >
          <option value="recent">Plus récents</option>
          <option value="oldest">Plus anciens</option>
          <option value="helpful">Plus utiles</option>
          <option value="rating">Note</option>
        </select>

        <label className="flex items-center space-x-2 text-sm">
          <input
            type="checkbox"
            checked={showOnlyVerified}
            onChange={(e) => setShowOnlyVerified(e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
          <span>Achats vérifiés uniquement</span>
        </label>
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const getRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `Il y a ${diffInHours}h`;
    } else if (diffInHours < 168) {
      return `Il y a ${Math.floor(diffInHours / 24)}j`;
    } else {
      return formatDate(dateString);
    }
  };

  return (
    <div className={`rating-system ${className}`}>
      {/* Rating Breakdown */}
      {renderRatingBreakdown()}

      {/* Review Form */}
      {allowReviews && isAuthenticated && (
        <div className="mb-8">
          {!showReviewForm ? (
            <button
              onClick={() => setShowReviewForm(true)}
              className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-primary-400 hover:text-primary-600 transition-colors"
            >
              {editingReview ? 'Modifier mon avis' : 'Laisser un avis'}
            </button>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4">
                {editingReview ? 'Modifier votre avis' : 'Laisser un avis'}
              </h3>
              
              <form onSubmit={handleSubmit(onSubmitReview)} className="space-y-4">
                {/* Rating Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Note *
                  </label>
                  <div className="flex items-center space-x-2">
                    {renderStars(rating, true, 24)}
                    <span className="text-sm text-gray-600 ml-4">
                      {rating > 0 ? `${rating}/5` : 'Sélectionnez une note'}
                    </span>
                  </div>
                  {errors.rating && touched.rating && (
                    <p className="text-red-600 text-sm mt-1">{errors.rating}</p>
                  )}
                </div>

                {/* Title Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Titre de l'avis *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Résumez votre expérience..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                  {errors.title && touched.title && (
                    <p className="text-red-600 text-sm mt-1">{errors.title}</p>
                  )}
                </div>

                {/* Comment Input */}
                <FormTextarea
                  name="comment"
                  label="Votre avis *"
                  placeholder="Partagez votre expérience en détail..."
                  value={formData.comment}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={errors.comment}
                  touched={touched.comment}
                  rows={4}
                  maxLength={1000}
                />

                {/* Submit Error */}
                {errors.submit && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3">
                      <AlertTriangle size={20} className="text-red-600" />
                      <p className="text-red-700">{errors.submit}</p>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={cancelEdit}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Annuler
                  </button>
                  <FormButton
                    type="submit"
                    variant="primary"
                    loading={submitLoading}
                    disabled={rating === 0}
                  >
                    {editingReview ? 'Modifier' : 'Publier'} l'avis
                  </FormButton>
                </div>
              </form>
            </div>
          )}
        </div>
      )}

      {/* Filters */}
      {renderFilters()}

      {/* Reviews List */}
      <div className="space-y-6">
        {reviewsLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/6 mb-4"></div>
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-full"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : reviews.length === 0 ? (
          <div className="text-center py-12">
            <Star size={48} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucun avis pour le moment
            </h3>
            <p className="text-gray-600">
              {allowReviews && isAuthenticated 
                ? 'Soyez le premier à laisser un avis !' 
                : 'Les avis apparaîtront ici.'
              }
            </p>
          </div>
        ) : (
          reviews.map((review) => (
            <div key={review.id} className="bg-white rounded-lg border border-gray-200 p-6">
              {/* Review Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start space-x-4">
                  <div className="relative">
                    <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                      {review.author.avatar ? (
                        <img 
                          src={review.author.avatar} 
                          alt={review.author.firstName}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-primary-600 font-medium">
                          {review.author.firstName[0]}{review.author.lastName[0]}
                        </span>
                      )}
                    </div>
                    {review.author.isVerified && (
                      <CheckCircle size={16} className="absolute -bottom-1 -right-1 text-blue-500 bg-white rounded-full" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900">
                        {review.author.firstName} {review.author.lastName}
                      </h4>
                      {review.metadata.verified && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                          Achat vérifié
                        </span>
                      )}
                      {review.isPinned && (
                        <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                          Épinglé
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        {renderStars(review.rating)}
                      </div>
                      <span className="flex items-center space-x-1">
                        <Calendar size={12} />
                        <span>{getRelativeTime(review.createdAt)}</span>
                      </span>
                      {review.isEdited && (
                        <span className="text-gray-500">(modifié)</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Review Actions */}
                <div className="flex items-center space-x-2">
                  {user?.id === review.author.id && (
                    <button
                      onClick={() => startEdit(review)}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      title="Modifier"
                    >
                      <Edit size={16} />
                    </button>
                  )}
                  <button
                    onClick={() => handleReport(review.id, 'inappropriate')}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    title="Signaler"
                  >
                    <Flag size={16} />
                  </button>
                </div>
              </div>

              {/* Review Content */}
              <div className="mb-4">
                <h5 className="font-medium text-gray-900 mb-2">
                  {review.title}
                </h5>
                <p className="text-gray-700 leading-relaxed">
                  {review.comment}
                </p>
              </div>

              {/* Review Footer */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => handleLike(review.id)}
                    className={`flex items-center space-x-2 text-sm transition-colors ${
                      review.isLiked 
                        ? 'text-blue-600' 
                        : 'text-gray-600 hover:text-blue-600'
                    }`}
                  >
                    <ThumbsUp size={16} fill={review.isLiked ? 'currentColor' : 'none'} />
                    <span>{review.likes}</span>
                  </button>
                  
                  <button
                    onClick={() => handleLike(review.id)}
                    className={`flex items-center space-x-2 text-sm transition-colors ${
                      review.isDisliked 
                        ? 'text-red-600' 
                        : 'text-gray-600 hover:text-red-600'
                    }`}
                  >
                    <ThumbsDown size={16} fill={review.isDisliked ? 'currentColor' : 'none'} />
                    <span>{review.dislikes}</span>
                  </button>

                  {allowReplies && (
                    <button
                      onClick={() => setReplyingTo(replyingTo === review.id ? null : review.id)}
                      className="flex items-center space-x-2 text-sm text-gray-600 hover:text-primary-600 transition-colors"
                    >
                      <Reply size={16} />
                      <span>Répondre</span>
                    </button>
                  )}
                </div>

                <div className="text-sm text-gray-500">
                  {review.metadata.helpful > 0 && (
                    <span>{review.metadata.helpful} personnes ont trouvé cet avis utile</span>
                  )}
                </div>
              </div>

              {/* Replies */}
              {review.replies.length > 0 && (
                <div className="mt-6 pl-8 border-l-2 border-gray-100 space-y-4">
                  {review.replies.map((reply) => (
                    <div key={reply.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                          {reply.author.avatar ? (
                            <img 
                              src={reply.author.avatar} 
                              alt={reply.author.firstName}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                          ) : (
                            <span className="text-primary-600 font-medium text-sm">
                              {reply.author.firstName[0]}{reply.author.lastName[0]}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-gray-900 text-sm">
                              {reply.author.firstName} {reply.author.lastName}
                            </span>
                            {reply.author.role === 'BUSINESS_OWNER' && (
                              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                Propriétaire
                              </span>
                            )}
                            <span className="text-xs text-gray-500">
                              {getRelativeTime(reply.createdAt)}
                            </span>
                          </div>
                          <p className="text-gray-700 text-sm leading-relaxed">
                            {reply.comment}
                          </p>
                          
                          <div className="flex items-center space-x-2 mt-2">
                            <button
                              onClick={() => handleLike(reply.id, true)}
                              className={`flex items-center space-x-1 text-xs transition-colors ${
                                reply.isLiked 
                                  ? 'text-blue-600' 
                                  : 'text-gray-500 hover:text-blue-600'
                              }`}
                            >
                              <ThumbsUp size={12} fill={reply.isLiked ? 'currentColor' : 'none'} />
                              <span>{reply.likes}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Reply Form */}
              {replyingTo === review.id && isAuthenticated && (
                <div className="mt-6 pl-8 border-l-2 border-primary-200">
                  <div className="bg-primary-50 rounded-lg p-4">
                    <h6 className="font-medium text-gray-900 mb-3">Répondre à cet avis</h6>
                    <FormTextarea
                      name="replyComment"
                      placeholder="Votre réponse..."
                      value=""
                      onChange={() => {}}
                      rows={3}
                      maxLength={500}
                    />
                    <div className="flex justify-end space-x-2 mt-3">
                      <button
                        onClick={() => setReplyingTo(null)}
                        className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                      >
                        Annuler
                      </button>
                      <FormButton
                        type="button"
                        variant="primary"
                        size="sm"
                      >
                        Répondre
                      </FormButton>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}