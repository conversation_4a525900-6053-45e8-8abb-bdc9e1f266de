#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Configuration de LeClub...\n');

// Check if PostgreSQL is running
try {
  console.log('1. Vérification de PostgreSQL...');
  execSync('psql --version', { stdio: 'pipe' });
  console.log('   ✅ PostgreSQL est installé');
} catch (error) {
  console.log('   ❌ PostgreSQL n\'est pas installé ou pas dans le PATH');
  console.log('   📋 Vous devez installer PostgreSQL d\'abord');
  process.exit(1);
}

// Check if .env files exist
console.log('\n2. Vérification des fichiers de configuration...');
const envFiles = [
  'server/.env',
  'client/.env.local'
];

envFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} existe`);
  } else {
    console.log(`   ❌ ${file} manquant`);
  }
});

// Try to create database
console.log('\n3. Création de la base de données...');
try {
  // Try different common PostgreSQL setups
  const dbCommands = [
    'createdb leclub_db',
    'createdb -U postgres leclub_db',
    'psql -U postgres -c "CREATE DATABASE leclub_db;"'
  ];
  
  let dbCreated = false;
  for (const cmd of dbCommands) {
    try {
      execSync(cmd, { stdio: 'pipe' });
      console.log(`   ✅ Base de données créée avec: ${cmd}`);
      dbCreated = true;
      break;
    } catch (err) {
      // Try next command
    }
  }
  
  if (!dbCreated) {
    console.log('   ⚠️  Impossible de créer la base automatiquement');
    console.log('   📋 Créez manuellement la base "leclub_db" dans PostgreSQL');
  }
} catch (error) {
  console.log('   ⚠️  Erreur lors de la création de la base de données');
}

// Install dependencies
console.log('\n4. Installation des dépendances serveur...');
try {
  process.chdir('server');
  execSync('npm install', { stdio: 'inherit' });
  console.log('   ✅ Dépendances serveur installées');
} catch (error) {
  console.log('   ❌ Erreur installation serveur');
}

// Run Prisma migration
console.log('\n5. Migration de la base de données...');
try {
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit' });
  console.log('   ✅ Migration réussie');
} catch (error) {
  console.log('   ❌ Erreur de migration - vérifiez la connexion à la base');
  console.log('   💡 Vous devrez peut-être ajuster DATABASE_URL dans server/.env');
}

// Generate Prisma client
console.log('\n6. Génération du client Prisma...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('   ✅ Client Prisma généré');
} catch (error) {
  console.log('   ❌ Erreur génération Prisma');
}

process.chdir('..');

console.log('\n🎉 Configuration terminée!');
console.log('\n📋 Prochaines étapes:');
console.log('1. Vérifiez server/.env si des erreurs de base de données');
console.log('2. Lancez: npm run dev');
console.log('3. Ouvrez: http://localhost:3000');
console.log('\n💡 En cas de problème, ajustez DATABASE_URL dans server/.env');