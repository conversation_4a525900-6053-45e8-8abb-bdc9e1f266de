'use client';

import React, { useState, useEffect } from 'react';
import { AlertTriangle, X, ExternalLink, Volume2, VolumeX } from 'lucide-react';
import { useNotifications } from '@/contexts/NotificationContext';
import { EmergencyNotification } from '@/lib/socket';
import Link from 'next/link';

interface EmergencyBannerProps {
  className?: string;
}

export default function EmergencyBanner({ className = '' }: EmergencyBannerProps) {
  const { notifications, markAsRead, removeNotification } = useNotifications();
  const [dismissedIds, setDismissedIds] = useState<Set<string>>(new Set());
  const [soundEnabled, setSoundEnabled] = useState(true);

  // Filter for emergency notifications that haven't been dismissed
  const emergencyNotifications = notifications.filter(
    (notification): notification is EmergencyNotification => 
      notification.type === 'emergency' && 
      !dismissedIds.has(notification.id) &&
      (!(notification as EmergencyNotification).expiresAt || new Date((notification as EmergencyNotification).expiresAt!) > new Date())
  );

  // Get the most critical notification
  const activeNotification = emergencyNotifications
    .sort((a, b) => {
      const priorityOrder = { critical: 0, high: 1 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    })[0];

  // Play sound for new emergency notifications
  useEffect(() => {
    if (activeNotification && soundEnabled && 'Audio' in window) {
      try {
        // Create a simple beep sound using Web Audio API
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.2);
        
        // Play multiple beeps for critical alerts
        if (activeNotification.priority === 'critical') {
          setTimeout(() => {
            const oscillator2 = audioContext.createOscillator();
            const gainNode2 = audioContext.createGain();
            
            oscillator2.connect(gainNode2);
            gainNode2.connect(audioContext.destination);
            
            oscillator2.frequency.setValueAtTime(800, audioContext.currentTime);
            gainNode2.gain.setValueAtTime(0.1, audioContext.currentTime);
            
            oscillator2.start();
            oscillator2.stop(audioContext.currentTime + 0.2);
          }, 300);
        }
      } catch (error) {
        console.warn('Could not play emergency notification sound:', error);
      }
    }
  }, [activeNotification?.id, soundEnabled]);

  const handleDismiss = (notificationId: string) => {
    setDismissedIds(prev => new Set(prev).add(notificationId));
    markAsRead(notificationId);
  };

  const handleRemove = (notificationId: string) => {
    removeNotification(notificationId);
  };

  const getPriorityColor = (priority: 'critical' | 'high') => {
    return priority === 'critical' 
      ? 'bg-red-600 border-red-700' 
      : 'bg-orange-500 border-orange-600';
  };

  const getPriorityTextColor = (priority: 'critical' | 'high') => {
    return priority === 'critical' ? 'text-red-100' : 'text-orange-100';
  };

  if (!activeNotification) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      <div className={`${getPriorityColor(activeNotification.priority)} text-white`}>
        {/* Animated background for critical alerts */}
        {activeNotification.priority === 'critical' && (
          <div className="absolute inset-0 bg-red-700 opacity-20 animate-pulse" />
        )}
        
        <div className="relative px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1">
              {/* Icon */}
              <div className="flex-shrink-0">
                <AlertTriangle 
                  size={24} 
                  className={`${
                    activeNotification.priority === 'critical' 
                      ? 'animate-bounce' 
                      : ''
                  }`} 
                />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm font-bold uppercase tracking-wide">
                    {activeNotification.priority === 'critical' ? '🚨 URGENT' : '⚠️ IMPORTANT'}
                  </span>
                  {activeNotification.area && (
                    <span className={`text-xs px-2 py-1 rounded ${getPriorityTextColor(activeNotification.priority)} bg-black bg-opacity-20`}>
                      {activeNotification.area}
                    </span>
                  )}
                </div>
                <h3 className="text-lg font-semibold leading-tight">
                  {activeNotification.title}
                </h3>
                <p className="text-sm opacity-90 mt-1">
                  {activeNotification.message}
                </p>
                
                {/* Expiration time */}
                {activeNotification.expiresAt && (
                  <p className="text-xs opacity-75 mt-2">
                    Expire le {new Date(activeNotification.expiresAt).toLocaleString('fr-FR')}
                  </p>
                )}
              </div>

              {/* Action button */}
              {activeNotification.actionUrl && (
                <div className="flex-shrink-0">
                  <Link
                    href={activeNotification.actionUrl}
                    className="inline-flex items-center space-x-2 bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    onClick={() => handleDismiss(activeNotification.id)}
                  >
                    <span>{activeNotification.actionText || 'En savoir plus'}</span>
                    <ExternalLink size={16} />
                  </Link>
                </div>
              )}
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-2 ml-4">
              {/* Sound toggle */}
              <button
                onClick={() => setSoundEnabled(!soundEnabled)}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                title={soundEnabled ? 'Désactiver le son' : 'Activer le son'}
              >
                {soundEnabled ? <Volume2 size={16} /> : <VolumeX size={16} />}
              </button>

              {/* Dismiss button */}
              <button
                onClick={() => handleDismiss(activeNotification.id)}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                title="Masquer temporairement"
              >
                <X size={16} />
              </button>

              {/* Remove button */}
              <button
                onClick={() => handleRemove(activeNotification.id)}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                title="Supprimer définitivement"
              >
                <X size={16} className="text-white opacity-60" />
              </button>
            </div>
          </div>

          {/* Multiple notifications indicator */}
          {emergencyNotifications.length > 1 && (
            <div className="mt-3 pt-3 border-t border-white border-opacity-20">
              <p className="text-xs opacity-75">
                {emergencyNotifications.length - 1} autre{emergencyNotifications.length > 2 ? 's' : ''} alerte{emergencyNotifications.length > 2 ? 's' : ''} d'urgence
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}