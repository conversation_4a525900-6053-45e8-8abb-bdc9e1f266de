import express from 'express';
import { body } from 'express-validator';
import { 
  getNews, 
  getNewsById, 
  createNews, 
  updateNews, 
  deleteNews 
} from '../controllers/newsController';
import { protect, authorize } from '../middleware/auth';

const router = express.Router();

// Public routes
router.get('/', getNews);
router.get('/:id', getNewsById);

// Protected routes (Admin only)
router.post('/', protect, authorize('ADMIN'), [
  body('title').notEmpty().withMessage('Le titre est requis'),
  body('content').notEmpty().withMessage('Le contenu est requis'),
  body('category').isIn(['OFFICIAL', 'COMMUNITY', 'EVENTS', 'BUSINESS', 'EMERGENCY'])
    .withMessage('Catégorie invalide')
], createNews);

router.put('/:id', protect, authorize('ADMIN'), [
  body('title').optional().notEmpty().withMessage('Le titre ne peut pas être vide'),
  body('content').optional().notEmpty().withMessage('Le contenu ne peut pas être vide'),
  body('category').optional().isIn(['OFFICIAL', 'COMMUNITY', 'EVENTS', 'BUSINESS', 'EMERGENCY'])
    .withMessage('Catégorie invalide')
], updateNews);

router.delete('/:id', protect, authorize('ADMIN'), deleteNews);

export default router;