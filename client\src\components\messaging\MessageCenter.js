'use client';

import React, { useState, useEffect, useRef } from 'react';
import { 
  Send, 
  Search, 
  MoreHorizontal, 
  Phone, 
  Video, 
  Info, 
  Paperclip, 
  Smile, 
  Trash2, 
  Archive,
  Star,
  Reply,
  Forward,
  Edit,
  Check,
  CheckCheck,
  Circle,
  Image,
  File,
  MapPin,
  Calendar,
  User,
  Users,
  Plus,
  X,
  Settings
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useApi } from '@/hooks/useApi';
import api from '@/lib/api';

interface Message {
  id: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'location' | 'event';
  timestamp: string;
  senderId: string;
  conversationId: string;
  isRead: boolean;
  isDelivered: boolean;
  replyTo?: Message;
  attachments?: MessageAttachment[];
  metadata?: {
    location?: { lat: number; lng: number; address: string };
    event?: { id: string; title: string; date: string };
    editedAt?: string;
  };
}

interface MessageAttachment {
  id: string;
  type: 'image' | 'file';
  url: string;
  name: string;
  size: number;
  mimeType: string;
}

interface Conversation {
  id: string;
  type: 'direct' | 'group';
  title?: string;
  participants: ConversationParticipant[];
  lastMessage?: Message;
  unreadCount: number;
  isArchived: boolean;
  isPinned: boolean;
  isMuted: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ConversationParticipant {
  id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: string;
  role?: 'admin' | 'member';
}

interface MessageCenterProps {
  className?: string;
  initialConversationId?: string;
}

export default function MessageCenter({ 
  className = '',
  initialConversationId 
}: MessageCenterProps) {
  const { user, isAuthenticated } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageInput, setMessageInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewConversation, setShowNewConversation] = useState(false);
  const [showConversationInfo, setShowConversationInfo] = useState(false);
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  const [editingMessage, setEditingMessage] = useState<Message | null>(null);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [isTyping, setIsTyping] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // API hooks
  const { 
    data: conversationsData, 
    loading: conversationsLoading, 
    execute: loadConversations 
  } = useApi<Conversation[]>();

  const { 
    data: messagesData, 
    loading: messagesLoading, 
    execute: loadMessages 
  } = useApi<Message[]>();

  const { loading: sendLoading, execute: sendMessage } = useApi();
  const { execute: markAsRead } = useApi();
  const { execute: createConversation } = useApi();

  // Load conversations
  useEffect(() => {
    if (isAuthenticated) {
      loadConversations(() => api.get('/conversations'));
    }
  }, [isAuthenticated]);

  // Handle conversations data
  useEffect(() => {
    if (conversationsData) {
      setConversations(conversationsData);
      
      // Auto-select conversation
      if (initialConversationId) {
        const conversation = conversationsData.find(c => c.id === initialConversationId);
        if (conversation) {
          setActiveConversation(conversation);
        }
      } else if (!activeConversation && conversationsData.length > 0) {
        setActiveConversation(conversationsData[0]);
      }
    }
  }, [conversationsData, initialConversationId]);

  // Load messages when conversation changes
  useEffect(() => {
    if (activeConversation) {
      loadMessages(() => api.get(`/conversations/${activeConversation.id}/messages`));
    }
  }, [activeConversation]);

  // Handle messages data
  useEffect(() => {
    if (messagesData) {
      setMessages(messagesData);
      scrollToBottom();
      
      // Mark messages as read
      if (activeConversation) {
        markAsRead(() => api.put(`/conversations/${activeConversation.id}/read`));
      }
    }
  }, [messagesData, activeConversation]);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle sending messages
  const handleSendMessage = async () => {
    if (!messageInput.trim() || !activeConversation || sendLoading) return;

    const messageData = {
      content: messageInput.trim(),
      type: 'text' as const,
      conversationId: activeConversation.id,
      replyTo: replyingTo?.id
    };

    try {
      await sendMessage(() => api.post('/messages', messageData));
      setMessageInput('');
      setReplyingTo(null);
      await loadMessages(() => api.get(`/conversations/${activeConversation.id}/messages`));
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  // Handle file upload
  const handleFileUpload = async (files: FileList) => {
    if (!activeConversation || files.length === 0) return;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const formData = new FormData();
      formData.append('file', file);
      formData.append('conversationId', activeConversation.id);
      
      try {
        await sendMessage(() => api.post('/messages/upload', formData));
      } catch (error) {
        console.error('Failed to upload file:', error);
      }
    }
    
    await loadMessages(() => api.get(`/conversations/${activeConversation.id}/messages`));
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const diffHours = diff / (1000 * 60 * 60);
    
    if (diffHours < 24) {
      return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
    } else if (diffHours < 168) {
      return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' });
    }
  };

  // Get conversation title
  const getConversationTitle = (conversation: Conversation) => {
    if (conversation.type === 'group') {
      return conversation.title || 'Groupe sans nom';
    } else {
      const otherParticipant = conversation.participants.find(p => p.id !== user?.id);
      return otherParticipant ? `${otherParticipant.firstName} ${otherParticipant.lastName}` : 'Conversation';
    }
  };

  // Get conversation avatar
  const getConversationAvatar = (conversation: Conversation) => {
    if (conversation.type === 'group') {
      return (
        <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
          <Users size={20} className="text-white" />
        </div>
      );
    } else {
      const otherParticipant = conversation.participants.find(p => p.id !== user?.id);
      return (
        <div className="relative">
          <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
            {otherParticipant?.avatar ? (
              <img 
                src={otherParticipant.avatar} 
                alt={otherParticipant.firstName}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <span className="text-primary-600 font-medium">
                {otherParticipant?.firstName[0]}{otherParticipant?.lastName[0]}
              </span>
            )}
          </div>
          {otherParticipant?.isOnline && (
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
          )}
        </div>
      );
    }
  };

  // Render message status
  const renderMessageStatus = (message: Message) => {
    if (message.senderId !== user?.id) return null;
    
    if (message.isRead) {
      return <CheckCheck size={14} className="text-blue-500" />;
    } else if (message.isDelivered) {
      return <CheckCheck size={14} className="text-gray-400" />;
    } else {
      return <Check size={14} className="text-gray-400" />;
    }
  };

  // Filter conversations
  const filteredConversations = conversations.filter(conversation => {
    if (!searchQuery) return true;
    const title = getConversationTitle(conversation).toLowerCase();
    return title.includes(searchQuery.toLowerCase());
  });

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Connexion requise</h3>
          <p className="text-gray-600">Connectez-vous pour accéder à vos messages.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`message-center flex h-[600px] bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {/* Conversations Sidebar */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Messages</h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowNewConversation(true)}
                className="p-2 text-gray-400 hover:text-primary-600 transition-colors"
                title="Nouvelle conversation"
              >
                <Plus size={20} />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Settings size={20} />
              </button>
            </div>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher une conversation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          {conversationsLoading ? (
            <div className="p-4 space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredConversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <p>Aucune conversation</p>
            </div>
          ) : (
            filteredConversations.map((conversation) => (
              <button
                key={conversation.id}
                onClick={() => setActiveConversation(conversation)}
                className={`w-full p-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 ${
                  activeConversation?.id === conversation.id ? 'bg-primary-50 border-primary-200' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  {getConversationAvatar(conversation)}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-gray-900 truncate">
                        {getConversationTitle(conversation)}
                      </h3>
                      {conversation.lastMessage && (
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(conversation.lastMessage.timestamp)}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-600 truncate">
                        {conversation.lastMessage?.content || 'Aucun message'}
                      </p>
                      {conversation.unreadCount > 0 && (
                        <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded-full min-w-[20px] h-5 flex items-center justify-center">
                          {conversation.unreadCount}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </button>
            ))
          )}
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getConversationAvatar(activeConversation)}
                  <div>
                    <h3 className="font-medium text-gray-900">
                      {getConversationTitle(activeConversation)}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {activeConversation.type === 'group' 
                        ? `${activeConversation.participants.length} participants`
                        : activeConversation.participants.find(p => p.id !== user?.id)?.isOnline 
                        ? 'En ligne' 
                        : 'Hors ligne'
                      }
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <Phone size={20} />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <Video size={20} />
                  </button>
                  <button 
                    onClick={() => setShowConversationInfo(!showConversationInfo)}
                    className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <Info size={20} />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messagesLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-xs p-3 rounded-lg ${
                          i % 2 === 0 ? 'bg-primary-100' : 'bg-gray-100'
                        }`}>
                          <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                messages.map((message, index) => {
                  const isOwnMessage = message.senderId === user?.id;
                  const showAvatar = !isOwnMessage && (
                    index === 0 || 
                    messages[index - 1]?.senderId !== message.senderId
                  );
                  
                  return (
                    <div
                      key={message.id}
                      className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} ${
                        showAvatar ? 'mt-4' : 'mt-1'
                      }`}
                    >
                      {!isOwnMessage && showAvatar && (
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                          <span className="text-primary-600 font-medium text-sm">
                            {activeConversation.participants.find(p => p.id === message.senderId)?.firstName[0]}
                          </span>
                        </div>
                      )}
                      
                      <div className={`max-w-xs lg:max-w-md ${!isOwnMessage && !showAvatar ? 'ml-10' : ''}`}>
                        {message.replyTo && (
                          <div className="bg-gray-100 border-l-4 border-gray-400 p-2 mb-2 rounded">
                            <p className="text-xs text-gray-600 mb-1">
                              Réponse à {activeConversation.participants.find(p => p.id === message.replyTo?.senderId)?.firstName}
                            </p>
                            <p className="text-sm text-gray-800 truncate">
                              {message.replyTo.content}
                            </p>
                          </div>
                        )}
                        
                        <div className={`p-3 rounded-lg ${
                          isOwnMessage 
                            ? 'bg-primary-600 text-white' 
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          <p className="break-words">{message.content}</p>
                          
                          <div className={`flex items-center justify-between mt-2 text-xs ${
                            isOwnMessage ? 'text-primary-100' : 'text-gray-500'
                          }`}>
                            <span>{formatTimestamp(message.timestamp)}</span>
                            <div className="flex items-center space-x-1">
                              {message.metadata?.editedAt && (
                                <span>(modifié)</span>
                              )}
                              {renderMessageStatus(message)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Reply Bar */}
            {replyingTo && (
              <div className="px-4 py-2 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Reply size={16} className="text-gray-400" />
                  <span className="text-sm text-gray-600">
                    Réponse à {activeConversation.participants.find(p => p.id === replyingTo.senderId)?.firstName}
                  </span>
                  <span className="text-sm text-gray-800 truncate max-w-xs">
                    {replyingTo.content}
                  </span>
                </div>
                <button 
                  onClick={() => setReplyingTo(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={16} />
                </button>
              </div>
            )}

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-end space-x-2">
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Paperclip size={20} />
                </button>
                
                <div className="flex-1 relative">
                  <textarea
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    placeholder="Tapez votre message..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    rows={1}
                    style={{ minHeight: '40px', maxHeight: '120px' }}
                  />
                </div>
                
                <button
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim() || sendLoading}
                  className="p-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send size={20} />
                </button>
              </div>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*,application/pdf,.doc,.docx"
              onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
              className="hidden"
            />
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Send size={24} className="text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Sélectionnez une conversation
              </h3>
              <p className="text-gray-600">
                Choisissez une conversation dans la liste pour commencer à discuter.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}