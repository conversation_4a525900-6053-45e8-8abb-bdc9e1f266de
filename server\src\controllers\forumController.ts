import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get all forum categories
export const getCategories = async (req: Request, res: Response) => {
  try {
    const categories = await prisma.forumCategory.findMany({
      where: {
        isActive: true
      },
      include: {
        posts: {
          select: {
            id: true,
            title: true,
            createdAt: true,
            updatedAt: true,
            author: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true
              }
            },
            _count: {
              select: {
                comments: true
              }
            }
          },
          orderBy: {
            updatedAt: 'desc'
          },
          take: 5 // Latest 5 posts per category
        },
        _count: {
          select: {
            posts: true
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    });

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      message: 'E<PERSON>ur lors de la récupération des catégories'
    });
  }
};

// Get posts by category with pagination
export const getPosts = async (req: Request, res: Response) => {
  try {
    const { categoryId, page = '1', limit = '10', search } = req.query;
    
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const where: any = {};
    
    // Filter by category if provided
    if (categoryId) {
      where.categoryId = categoryId;
    }
    
    // Search in title and content if provided
    if (search) {
      where.OR = [
        {
          title: {
            contains: search as string,
            mode: 'insensitive'
          }
        },
        {
          content: {
            contains: search as string,
            mode: 'insensitive'
          }
        }
      ];
    }

    const [posts, totalCount] = await Promise.all([
      prisma.forumPost.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true
            }
          },
          category: {
            select: {
              id: true,
              name: true,
              color: true
            }
          },
          _count: {
            select: {
              comments: true
            }
          }
        },
        orderBy: [
          { isPinned: 'desc' },
          { updatedAt: 'desc' }
        ],
        skip,
        take: limitNum
      }),
      prisma.forumPost.count({ where })
    ]);

    res.json({
      success: true,
      data: posts,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        pages: Math.ceil(totalCount / limitNum)
      }
    });
  } catch (error) {
    console.error('Error fetching posts:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des posts'
    });
  }
};

// Get single post with comments
export const getPostById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Increment view count
    await prisma.forumPost.update({
      where: { id },
      data: {
        views: {
          increment: 1
        }
      }
    });

    const post = await prisma.forumPost.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
            bio: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        comments: {
          include: {
            author: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        },
        _count: {
          select: {
            comments: true
          }
        }
      }
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post non trouvé'
      });
    }

    res.json({
      success: true,
      data: post
    });
  } catch (error) {
    console.error('Error fetching post:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du post'
    });
  }
};

// Create new post
export const createPost = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { title, content, categoryId } = req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Check if category exists
    const category = await prisma.forumCategory.findUnique({
      where: { id: categoryId }
    });

    if (!category || !category.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Catégorie non trouvée ou inactive'
      });
    }

    const post = await prisma.forumPost.create({
      data: {
        title,
        content,
        authorId: userId,
        categoryId
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        _count: {
          select: {
            comments: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Post créé avec succès',
      data: post
    });
  } catch (error) {
    console.error('Error creating post:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création du post'
    });
  }
};

// Update post (author or admin only)
export const updatePost = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { title, content } = req.body;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if post exists
    const existingPost = await prisma.forumPost.findUnique({
      where: { id }
    });

    if (!existingPost) {
      return res.status(404).json({
        success: false,
        message: 'Post non trouvé'
      });
    }

    // Check if user is author or admin
    if (existingPost.authorId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    // Check if post is locked
    if (existingPost.isLocked && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Ce post est verrouillé'
      });
    }

    const post = await prisma.forumPost.update({
      where: { id },
      data: {
        title: title || existingPost.title,
        content: content || existingPost.content
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        _count: {
          select: {
            comments: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Post mis à jour avec succès',
      data: post
    });
  } catch (error) {
    console.error('Error updating post:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du post'
    });
  }
};

// Delete post (author or admin only)
export const deletePost = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if post exists
    const existingPost = await prisma.forumPost.findUnique({
      where: { id }
    });

    if (!existingPost) {
      return res.status(404).json({
        success: false,
        message: 'Post non trouvé'
      });
    }

    // Check if user is author or admin
    if (existingPost.authorId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    await prisma.forumPost.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Post supprimé avec succès'
    });
  } catch (error) {
    console.error('Error deleting post:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du post'
    });
  }
};

// Add comment to post
export const addComment = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { id } = req.params; // Post ID
    const { content } = req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Check if post exists and is not locked
    const post = await prisma.forumPost.findUnique({
      where: { id }
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post non trouvé'
      });
    }

    if (post.isLocked) {
      return res.status(403).json({
        success: false,
        message: 'Ce post est verrouillé, impossible d\'ajouter un commentaire'
      });
    }

    const comment = await prisma.forumComment.create({
      data: {
        content,
        authorId: userId,
        postId: id
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      }
    });

    // Update post's updatedAt to bump it in discussions
    await prisma.forumPost.update({
      where: { id },
      data: {
        updatedAt: new Date()
      }
    });

    res.status(201).json({
      success: true,
      message: 'Commentaire ajouté avec succès',
      data: comment
    });
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout du commentaire'
    });
  }
};

// Update comment (author or admin only)
export const updateComment = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { commentId } = req.params;
    const { content } = req.body;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if comment exists
    const existingComment = await prisma.forumComment.findUnique({
      where: { id: commentId },
      include: {
        post: true
      }
    });

    if (!existingComment) {
      return res.status(404).json({
        success: false,
        message: 'Commentaire non trouvé'
      });
    }

    // Check if user is author or admin
    if (existingComment.authorId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    // Check if post is locked
    if (existingComment.post.isLocked && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Ce post est verrouillé'
      });
    }

    const comment = await prisma.forumComment.update({
      where: { id: commentId },
      data: { content },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Commentaire mis à jour avec succès',
      data: comment
    });
  } catch (error) {
    console.error('Error updating comment:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du commentaire'
    });
  }
};

// Delete comment (author or admin only)
export const deleteComment = async (req: Request, res: Response) => {
  try {
    const { commentId } = req.params;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if comment exists
    const existingComment = await prisma.forumComment.findUnique({
      where: { id: commentId }
    });

    if (!existingComment) {
      return res.status(404).json({
        success: false,
        message: 'Commentaire non trouvé'
      });
    }

    // Check if user is author or admin
    if (existingComment.authorId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    await prisma.forumComment.delete({
      where: { id: commentId }
    });

    res.json({
      success: true,
      message: 'Commentaire supprimé avec succès'
    });
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du commentaire'
    });
  }
};

// Admin: Pin/Unpin post
export const togglePinPost = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = (req as any).user?.role;

    if (userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès administrateur requis'
      });
    }

    const post = await prisma.forumPost.findUnique({
      where: { id }
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post non trouvé'
      });
    }

    const updatedPost = await prisma.forumPost.update({
      where: { id },
      data: {
        isPinned: !post.isPinned
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: `Post ${updatedPost.isPinned ? 'épinglé' : 'désépinglé'} avec succès`,
      data: updatedPost
    });
  } catch (error) {
    console.error('Error toggling pin post:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la modification du post'
    });
  }
};

// Admin: Lock/Unlock post
export const toggleLockPost = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = (req as any).user?.role;

    if (userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès administrateur requis'
      });
    }

    const post = await prisma.forumPost.findUnique({
      where: { id }
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post non trouvé'
      });
    }

    const updatedPost = await prisma.forumPost.update({
      where: { id },
      data: {
        isLocked: !post.isLocked
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: `Post ${updatedPost.isLocked ? 'verrouillé' : 'déverrouillé'} avec succès`,
      data: updatedPost
    });
  } catch (error) {
    console.error('Error toggling lock post:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la modification du post'
    });
  }
};