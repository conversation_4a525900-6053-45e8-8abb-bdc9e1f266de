export interface ValidationRule {
  type: 'required' | 'email' | 'minLength' | 'maxLength' | 'pattern' | 'match' | 'custom';
  value?: string | number | RegExp;
  message: string;
  customValidator?: (value: any, formData?: any) => boolean;
}

export interface FieldValidation {
  [fieldName: string]: ValidationRule[];
}

export const commonValidations = {
  email: {
    type: 'email' as const,
    message: 'Format d\'email invalide'
  },
  required: (message: string = 'Ce champ est requis') => ({
    type: 'required' as const,
    message
  }),
  minLength: (length: number, message?: string) => ({
    type: 'minLength' as const,
    value: length,
    message: message || `Minimum ${length} caractères requis`
  }),
  maxLength: (length: number, message?: string) => ({
    type: 'maxLength' as const,
    value: length,
    message: message || `Maximum ${length} caractères autorisés`
  }),
  pattern: (regex: RegExp, message: string) => ({
    type: 'pattern' as const,
    value: regex,
    message
  }),
  match: (fieldName: string, message: string = 'Les champs ne correspondent pas') => ({
    type: 'match' as const,
    value: fieldName,
    message
  }),
  custom: (validator: (value: any, formData?: any) => boolean, message: string) => ({
    type: 'custom' as const,
    message,
    customValidator: validator
  })
};

export const validateField = (
  value: any,
  rules: ValidationRule[],
  formData?: any
): string | null => {
  for (const rule of rules) {
    switch (rule.type) {
      case 'required':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return rule.message;
        }
        break;

      case 'email':
        if (value && !/\S+@\S+\.\S+/.test(value)) {
          return rule.message;
        }
        break;

      case 'minLength':
        if (value && value.length < (rule.value as number)) {
          return rule.message;
        }
        break;

      case 'maxLength':
        if (value && value.length > (rule.value as number)) {
          return rule.message;
        }
        break;

      case 'pattern':
        if (value && !(rule.value as RegExp).test(value)) {
          return rule.message;
        }
        break;

      case 'match':
        if (formData && value !== formData[rule.value as string]) {
          return rule.message;
        }
        break;

      case 'custom':
        if (rule.customValidator && !rule.customValidator(value, formData)) {
          return rule.message;
        }
        break;
    }
  }
  return null;
};

export const validateForm = (
  formData: any,
  validationRules: FieldValidation
): { [key: string]: string } => {
  const errors: { [key: string]: string } = {};

  Object.keys(validationRules).forEach(fieldName => {
    const error = validateField(
      formData[fieldName],
      validationRules[fieldName],
      formData
    );
    if (error) {
      errors[fieldName] = error;
    }
  });

  return errors;
};

// Predefined validation sets for common forms
export const authValidation = {
  login: {
    email: [
      commonValidations.required('L\'email est requis'),
      commonValidations.email
    ],
    password: [
      commonValidations.required('Le mot de passe est requis'),
      commonValidations.minLength(6, 'Le mot de passe doit contenir au moins 6 caractères')
    ]
  },
  register: {
    firstName: [
      commonValidations.required('Le prénom est requis'),
      commonValidations.minLength(2, 'Le prénom doit contenir au moins 2 caractères'),
      commonValidations.maxLength(50, 'Le prénom ne peut pas dépasser 50 caractères')
    ],
    lastName: [
      commonValidations.required('Le nom est requis'),
      commonValidations.minLength(2, 'Le nom doit contenir au moins 2 caractères'),
      commonValidations.maxLength(50, 'Le nom ne peut pas dépasser 50 caractères')
    ],
    email: [
      commonValidations.required('L\'email est requis'),
      commonValidations.email
    ],
    password: [
      commonValidations.required('Le mot de passe est requis'),
      commonValidations.minLength(8, 'Le mot de passe doit contenir au moins 8 caractères'),
      commonValidations.pattern(
        /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre'
      )
    ],
    confirmPassword: [
      commonValidations.required('Veuillez confirmer votre mot de passe'),
      commonValidations.match('password', 'Les mots de passe ne correspondent pas')
    ]
  }
};

export const forumValidation = {
  newPost: {
    title: [
      commonValidations.required('Le titre est requis'),
      commonValidations.minLength(5, 'Le titre doit contenir au moins 5 caractères'),
      commonValidations.maxLength(100, 'Le titre ne peut pas dépasser 100 caractères')
    ],
    content: [
      commonValidations.required('Le contenu est requis'),
      commonValidations.minLength(20, 'Le contenu doit contenir au moins 20 caractères'),
      commonValidations.maxLength(5000, 'Le contenu ne peut pas dépasser 5000 caractères')
    ],
    categoryId: [
      commonValidations.required('Veuillez sélectionner une catégorie')
    ]
  },
  comment: {
    content: [
      commonValidations.required('Le commentaire est requis'),
      commonValidations.minLength(5, 'Le commentaire doit contenir au moins 5 caractères'),
      commonValidations.maxLength(1000, 'Le commentaire ne peut pas dépasser 1000 caractères')
    ]
  }
};

export const profileValidation = {
  update: {
    firstName: [
      commonValidations.required('Le prénom est requis'),
      commonValidations.minLength(2, 'Le prénom doit contenir au moins 2 caractères'),
      commonValidations.maxLength(50, 'Le prénom ne peut pas dépasser 50 caractères')
    ],
    lastName: [
      commonValidations.required('Le nom est requis'),
      commonValidations.minLength(2, 'Le nom doit contenir au moins 2 caractères'),
      commonValidations.maxLength(50, 'Le nom ne peut pas dépasser 50 caractères')
    ],
    email: [
      commonValidations.required('L\'email est requis'),
      commonValidations.email
    ],
    phone: [
      commonValidations.pattern(
        /^(?:\+33|0)[1-9](?:[0-9]{8})$/,
        'Format de téléphone invalide (ex: 01 23 45 67 89)'
      )
    ],
    address: [
      commonValidations.maxLength(200, 'L\'adresse ne peut pas dépasser 200 caractères')
    ]
  },
  changePassword: {
    currentPassword: [
      commonValidations.required('Le mot de passe actuel est requis')
    ],
    newPassword: [
      commonValidations.required('Le nouveau mot de passe est requis'),
      commonValidations.minLength(8, 'Le mot de passe doit contenir au moins 8 caractères'),
      commonValidations.pattern(
        /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre'
      )
    ],
    confirmPassword: [
      commonValidations.required('Veuillez confirmer le nouveau mot de passe'),
      commonValidations.match('newPassword', 'Les mots de passe ne correspondent pas')
    ]
  }
};

export const businessValidation = {
  create: {
    name: [
      commonValidations.required('Le nom de l\'entreprise est requis'),
      commonValidations.minLength(2, 'Le nom doit contenir au moins 2 caractères'),
      commonValidations.maxLength(100, 'Le nom ne peut pas dépasser 100 caractères')
    ],
    description: [
      commonValidations.required('La description est requise'),
      commonValidations.minLength(20, 'La description doit contenir au moins 20 caractères'),
      commonValidations.maxLength(1000, 'La description ne peut pas dépasser 1000 caractères')
    ],
    category: [
      commonValidations.required('Veuillez sélectionner une catégorie')
    ],
    address: [
      commonValidations.required('L\'adresse est requise'),
      commonValidations.minLength(10, 'L\'adresse doit contenir au moins 10 caractères')
    ],
    phone: [
      commonValidations.required('Le téléphone est requis'),
      commonValidations.pattern(
        /^(?:\+33|0)[1-9](?:[0-9]{8})$/,
        'Format de téléphone invalide (ex: 01 23 45 67 89)'
      )
    ],
    email: [
      commonValidations.required('L\'email est requis'),
      commonValidations.email
    ]
  }
};

export const newsValidation = {
  create: {
    title: [
      commonValidations.required('Le titre est requis'),
      commonValidations.minLength(5, 'Le titre doit contenir au moins 5 caractères'),
      commonValidations.maxLength(200, 'Le titre ne peut pas dépasser 200 caractères')
    ],
    excerpt: [
      commonValidations.required('Le résumé est requis'),
      commonValidations.minLength(20, 'Le résumé doit contenir au moins 20 caractères'),
      commonValidations.maxLength(500, 'Le résumé ne peut pas dépasser 500 caractères')
    ],
    content: [
      commonValidations.required('Le contenu est requis'),
      commonValidations.minLength(50, 'Le contenu doit contenir au moins 50 caractères')
    ],
    category: [
      commonValidations.required('Veuillez sélectionner une catégorie')
    ]
  }
};

export const reviewValidation = {
  title: [
    commonValidations.required('Le titre de l\'avis est requis'),
    commonValidations.minLength(5, 'Le titre doit contenir au moins 5 caractères'),
    commonValidations.maxLength(100, 'Le titre ne peut pas dépasser 100 caractères')
  ],
  comment: [
    commonValidations.required('Le commentaire est requis'),
    commonValidations.minLength(10, 'Le commentaire doit contenir au moins 10 caractères'),
    commonValidations.maxLength(1000, 'Le commentaire ne peut pas dépasser 1000 caractères')
  ],
  rating: [
    commonValidations.required('Veuillez donner une note'),
    commonValidations.custom(
      (value: number) => value >= 1 && value <= 5,
      'La note doit être comprise entre 1 et 5'
    )
  ]
};