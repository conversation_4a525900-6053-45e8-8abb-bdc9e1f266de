// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  password    String
  firstName   String
  lastName    String
  avatar      String?
  bio         String?
  phone       String?
  address     String?
  role        UserRole @default(RESIDENT)
  isVerified  Boolean  @default(false)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  businesses    Business[]
  posts         ForumPost[]
  comments      ForumComment[]
  events        Event[]
  eventAttendees EventAttendee[]
  newsArticles  News[]
  products      Product[]
  orders        Order[]
  reviews       Review[]
  jobPostings   JobPosting[]
  notifications Notification[]

  @@map("users")
}

enum UserRole {
  RESIDENT
  BUSINESS_OWNER
  ADMIN
}

// Business model
model Business {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String
  address     String
  phone       String?
  email       String?
  website     String?
  hours       Json? // Store opening hours as JSON
  logo        String?
  images      String[]
  isVerified  Boolean  @default(false)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  owner     User      @relation(fields: [ownerId], references: [id])
  ownerId   String
  products  Product[]
  reviews   Review[]
  jobs      JobPosting[]

  @@map("businesses")
}

// News model
model News {
  id          String      @id @default(cuid())
  title       String
  content     String
  excerpt     String?
  image       String?
  category    NewsCategory
  isPublished Boolean     @default(false)
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  author   User   @relation(fields: [authorId], references: [id])
  authorId String

  @@map("news")
}

enum NewsCategory {
  OFFICIAL
  COMMUNITY
  EVENTS
  BUSINESS
  EMERGENCY
}

// Events model
model Event {
  id          String   @id @default(cuid())
  title       String
  description String
  location    String
  date        DateTime
  endDate     DateTime?
  image       String?
  category    String
  maxAttendees Int?
  isPublic    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organizer   User   @relation(fields: [organizerId], references: [id])
  organizerId String
  attendees   EventAttendee[]

  @@map("events")
}

model EventAttendee {
  id       String           @id @default(cuid())
  status   AttendeeStatus   @default(INTERESTED)
  joinedAt DateTime         @default(now())

  // Relations
  event   Event  @relation(fields: [eventId], references: [id], onDelete: Cascade)
  eventId String
  user    User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId  String

  @@unique([eventId, userId])
  @@map("event_attendees")
}

enum AttendeeStatus {
  INTERESTED
  ATTENDING
  NOT_ATTENDING
}

// Forum models
model ForumCategory {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  color       String?
  order       Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())

  // Relations
  posts ForumPost[]

  @@map("forum_categories")
}

model ForumPost {
  id        String   @id @default(cuid())
  title     String
  content   String
  isPinned  Boolean  @default(false)
  isLocked  Boolean  @default(false)
  views     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  author     User            @relation(fields: [authorId], references: [id])
  authorId   String
  category   ForumCategory   @relation(fields: [categoryId], references: [id])
  categoryId String
  comments   ForumComment[]

  @@map("forum_posts")
}

model ForumComment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  author   User      @relation(fields: [authorId], references: [id])
  authorId String
  post     ForumPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  postId   String

  @@map("forum_comments")
}

// Marketplace models
model Product {
  id          String        @id @default(cuid())
  name        String
  description String
  price       Decimal       @db.Decimal(10, 2)
  category    String
  images      String[]
  stock       Int           @default(0)
  isActive    Boolean       @default(true)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  seller     User        @relation(fields: [sellerId], references: [id])
  sellerId   String
  business   Business?   @relation(fields: [businessId], references: [id])
  businessId String?
  orderItems OrderItem[]
  reviews    Review[]

  @@map("products")
}

model Order {
  id            String      @id @default(cuid())
  total         Decimal     @db.Decimal(10, 2)
  status        OrderStatus @default(PENDING)
  paymentMethod String?
  paymentId     String?
  notes         String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relations
  buyer User        @relation(fields: [buyerId], references: [id])
  buyerId String
  items OrderItem[]

  @@map("orders")
}

model OrderItem {
  id       String  @id @default(cuid())
  quantity Int
  price    Decimal @db.Decimal(10, 2)

  // Relations
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId   String
  product   Product @relation(fields: [productId], references: [id])
  productId String

  @@map("order_items")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PREPARING
  READY
  COMPLETED
  CANCELLED
}

// Reviews model
model Review {
  id        String   @id @default(cuid())
  rating    Int      // 1-5 stars
  comment   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  author     User      @relation(fields: [authorId], references: [id])
  authorId   String
  business   Business? @relation(fields: [businessId], references: [id])
  businessId String?
  product    Product?  @relation(fields: [productId], references: [id])
  productId  String?

  @@map("reviews")
}

// Jobs model
model JobPosting {
  id          String    @id @default(cuid())
  title       String
  description String
  type        JobType
  salary      String?
  location    String?
  requirements String?
  isActive    Boolean   @default(true)
  expiresAt   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  employer   User      @relation(fields: [employerId], references: [id])
  employerId String
  business   Business? @relation(fields: [businessId], references: [id])
  businessId String?

  @@map("job_postings")
}

enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  INTERNSHIP
  VOLUNTEER
}

// Notifications model
model Notification {
  id        String           @id @default(cuid())
  title     String
  message   String
  type      NotificationType
  isRead    Boolean          @default(false)
  data      Json?            // Additional data for the notification
  createdAt DateTime         @default(now())

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  @@map("notifications")
}

enum NotificationType {
  EMERGENCY
  NEWS
  EVENT
  FORUM
  MARKETPLACE
  GENERAL
}