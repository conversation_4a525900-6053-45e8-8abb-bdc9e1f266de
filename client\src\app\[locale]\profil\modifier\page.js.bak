'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { 
  ChevronLeft, 
  Camera, 
  Save, 
  X,
  AlertTriangle,
  Eye,
  EyeOff
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useFormValidation } from '@/hooks/useFormValidation';
import { profileValidation } from '@/lib/validation';
import { FormInput, FormTextarea, FormButton } from '@/components/forms/FormField';
import { useApiMutation } from '@/hooks/useApi';
import api from '@/lib/api';

export default function EditProfilePage() {
  const router = useRouter();
  const { user, isAuthenticated, updateProfile } = useAuth();
  const [activeTab, setActiveTab] = useState('personal');
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  // Form validation for profile data
  const {
    formData: profileData,
    errors: profileErrors,
    touched: profileTouched,
    handleChange: handleProfileChange,
    handleBlur: handleProfileBlur,
    handleSubmit: handleProfileSubmit,
    setError: setProfileError,
    reset: resetProfile
  } = useFormValidation({
    validationRules: profileValidation.update,
    initialData: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      address: user?.address || '',
      bio: user?.bio || ''
    },
    validateOnBlur: true
  });

  // Form validation for password change
  const {
    formData: passwordData,
    errors: passwordErrors,
    touched: passwordTouched,
    handleChange: handlePasswordChange,
    handleBlur: handlePasswordBlur,
    handleSubmit: handlePasswordSubmit,
    setError: setPasswordError,
    reset: resetPassword
  } = useFormValidation({
    validationRules: profileValidation.changePassword,
    initialData: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    },
    validateOnBlur: true
  });

  // API mutations
  const { mutate: updateProfileMutation, loading: updateLoading } = useApiMutation({
    onSuccess: async (response) => {
      const result = await updateProfile(response.data);
      if (result.success) {
        router.push('/profil');
      } else {
        setProfileError('submit', result.message || 'Erreur lors de la mise à jour');
      }
    },
    onError: (error) => {
      setProfileError('submit', error.message);
    }
  });

  const { mutate: changePasswordMutation, loading: passwordLoading } = useApiMutation({
    onSuccess: () => {
      resetPassword();
      setActiveTab('personal');
      // TODO: Show success toast
    },
    onError: (error) => {
      setPasswordError('submit', error.message);
    }
  });

  const { mutate: uploadAvatarMutation, loading: avatarLoading } = useApiMutation({
    onSuccess: (response) => {
      setAvatarPreview(response.data.url);
      // TODO: Update user context with new avatar
    },
    onError: (error) => {
      console.error('Avatar upload error:', error);
    }
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/connexion');
      return;
    }

    if (user) {
      resetProfile({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phone: user.phone || '',
        address: user.address || '',
        bio: user.bio || ''
      });
    }
  }, [isAuthenticated, user, router, resetProfile]);

  const onProfileSubmit = async (data: any) => {
    await updateProfileMutation(() => api.updateProfile({
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      email: data.email.trim(),
      phone: data.phone?.trim() || null,
      address: data.address?.trim() || null,
      bio: data.bio?.trim() || null
    }));
  };

  const onPasswordSubmit = async (data: any) => {
    await changePasswordMutation(() => api.changePassword(
      data.currentPassword,
      data.newPassword
    ));
  };

  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file
    if (!file.type.startsWith('image/')) {
      setProfileError('avatar', 'Veuillez sélectionner une image valide');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setProfileError('avatar', 'L\'image ne doit pas dépasser 5 MB');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setAvatarPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload to server
    await uploadAvatarMutation(() => api.uploadFile(file, 'avatar'));
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Connexion requise</h1>
          <p className="text-gray-600 mb-6">Vous devez être connecté pour modifier votre profil.</p>
          <Link
            href="/connexion"
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            Se connecter
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Navigation */}
      <div className="flex items-center space-x-2 mb-6 text-sm text-gray-600">
        <Link href="/profil" className="hover:text-primary-600 flex items-center space-x-1">
          <ChevronLeft size={16} />
          <span>Mon profil</span>
        </Link>
        <span>/</span>
        <span className="text-gray-900">Modifier</span>
      </div>

      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border">
          {/* Header */}
          <div className="p-6 border-b">
            <h1 className="text-2xl font-bold text-gray-900">Modifier mon profil</h1>
            <p className="text-gray-600 mt-2">
              Gérez vos informations personnelles et paramètres de sécurité.
            </p>
          </div>

          {/* Tabs */}
          <div className="border-b">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('personal')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'personal'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Informations personnelles
              </button>
              <button
                onClick={() => setActiveTab('security')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'security'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Sécurité
              </button>
            </nav>
          </div>

          {/* Personal Information Tab */}
          {activeTab === 'personal' && (
            <form onSubmit={handleProfileSubmit(onProfileSubmit)} className="p-6">
              {profileErrors.submit && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle size={20} className="text-red-600" />
                    <p className="text-red-700">{profileErrors.submit}</p>
                  </div>
                </div>
              )}

              {/* Avatar Section */}
              <div className="mb-8">
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Photo de profil
                </label>
                <div className="flex items-center space-x-6">
                  <div className="relative">
                    <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center overflow-hidden">
                      {avatarPreview || user.avatar ? (
                        <img 
                          src={avatarPreview || user.avatar} 
                          alt="Avatar" 
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-primary-600 font-bold text-xl">
                          {user.firstName[0]}{user.lastName[0]}
                        </span>
                      )}
                    </div>
                    {avatarLoading && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    )}
                  </div>
                  <div>
                    <input
                      type="file"
                      id="avatar"
                      accept="image/*"
                      onChange={handleAvatarChange}
                      className="hidden"
                      disabled={avatarLoading}
                    />
                    <label
                      htmlFor="avatar"
                      className="cursor-pointer inline-flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      <Camera size={16} />
                      <span>Changer la photo</span>
                    </label>
                    <p className="text-xs text-gray-500 mt-1">
                      JPG, PNG • Max 5 MB
                    </p>
                    {profileErrors.avatar && (
                      <p className="text-xs text-red-600 mt-1">{profileErrors.avatar}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Form Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <FormInput
                  name="firstName"
                  label="Prénom"
                  required
                  value={profileData.firstName}
                  onChange={handleProfileChange}
                  onBlur={handleProfileBlur}
                  error={profileErrors.firstName}
                  touched={profileTouched.firstName}
                />

                <FormInput
                  name="lastName"
                  label="Nom"
                  required
                  value={profileData.lastName}
                  onChange={handleProfileChange}
                  onBlur={handleProfileBlur}
                  error={profileErrors.lastName}
                  touched={profileTouched.lastName}
                />

                <FormInput
                  name="email"
                  type="email"
                  label="Adresse email"
                  required
                  value={profileData.email}
                  onChange={handleProfileChange}
                  onBlur={handleProfileBlur}
                  error={profileErrors.email}
                  touched={profileTouched.email}
                />

                <FormInput
                  name="phone"
                  type="tel"
                  label="Téléphone"
                  value={profileData.phone}
                  onChange={handleProfileChange}
                  onBlur={handleProfileBlur}
                  error={profileErrors.phone}
                  touched={profileTouched.phone}
                  placeholder="01 23 45 67 89"
                />
              </div>

              <div className="mb-6">
                <FormInput
                  name="address"
                  label="Adresse"
                  value={profileData.address}
                  onChange={handleProfileChange}
                  onBlur={handleProfileBlur}
                  error={profileErrors.address}
                  touched={profileTouched.address}
                  placeholder="Votre adresse complète"
                />
              </div>

              <div className="mb-8">
                <FormTextarea
                  name="bio"
                  label="Biographie"
                  rows={4}
                  value={profileData.bio}
                  onChange={handleProfileChange}
                  onBlur={handleProfileBlur}
                  error={profileErrors.bio}
                  touched={profileTouched.bio}
                  placeholder="Parlez-nous de vous..."
                  maxLength={500}
                  helpText="Présentez-vous à la communauté"
                />
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4">
                <Link
                  href="/profil"
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Annuler
                </Link>
                <FormButton
                  type="submit"
                  variant="primary"
                  loading={updateLoading}
                >
                  <Save size={16} />
                  Enregistrer
                </FormButton>
              </div>
            </form>
          )}

          {/* Security Tab */}
          {activeTab === 'security' && (
            <form onSubmit={handlePasswordSubmit(onPasswordSubmit)} className="p-6">
              {passwordErrors.submit && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle size={20} className="text-red-600" />
                    <p className="text-red-700">{passwordErrors.submit}</p>
                  </div>
                </div>
              )}

              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Changer le mot de passe</h3>
                <div className="space-y-6 max-w-md">
                  <FormInput
                    name="currentPassword"
                    type="password"
                    label="Mot de passe actuel"
                    required
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    onBlur={handlePasswordBlur}
                    error={passwordErrors.currentPassword}
                    touched={passwordTouched.currentPassword}
                    showPasswordToggle
                  />

                  <FormInput
                    name="newPassword"
                    type="password"
                    label="Nouveau mot de passe"
                    required
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    onBlur={handlePasswordBlur}
                    error={passwordErrors.newPassword}
                    touched={passwordTouched.newPassword}
                    showPasswordToggle
                    helpText="8 caractères minimum, avec majuscule, minuscule et chiffre"
                  />

                  <FormInput
                    name="confirmPassword"
                    type="password"
                    label="Confirmer le nouveau mot de passe"
                    required
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    onBlur={handlePasswordBlur}
                    error={passwordErrors.confirmPassword}
                    touched={passwordTouched.confirmPassword}
                    showPasswordToggle
                  />
                </div>
              </div>

              {/* Security Actions */}
              <div className="flex justify-end space-x-4">
                <FormButton
                  type="button"
                  variant="secondary"
                  onClick={() => resetPassword()}
                >
                  Annuler
                </FormButton>
                <FormButton
                  type="submit"
                  variant="primary"
                  loading={passwordLoading}
                >
                  Changer le mot de passe
                </FormButton>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}