export default function AdminPage() {
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Administration</h1>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">📰 Gestion Actualités</h2>
          <p className="text-gray-600 mb-4">C<PERSON>er et gérer les actualités communautaires.</p>
          <a href="/fr/admin/actualites" className="text-blue-600 hover:underline">Gérer les actualités →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">📅 Gestion Événements</h2>
          <p className="text-gray-600 mb-4">Organiser et superviser les événements locaux.</p>
          <a href="/fr/admin/evenements" className="text-blue-600 hover:underline">G<PERSON>rer les événements →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">👥 Utilisateurs</h2>
          <p className="text-gray-600 mb-4">Gérer les comptes utilisateurs.</p>
          <a href="/fr/admin/utilisateurs" className="text-blue-600 hover:underline">Voir les utilisateurs →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">🏢 Entreprises</h2>
          <p className="text-gray-600 mb-4">Approuver et gérer les entreprises.</p>
          <a href="/fr/admin/entreprises" className="text-blue-600 hover:underline">Gérer les entreprises →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">💬 Modération Forum</h2>
          <p className="text-gray-600 mb-4">Modérer les discussions communautaires.</p>
          <a href="/fr/admin/forum" className="text-blue-600 hover:underline">Modérer le forum →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">📊 Statistiques</h2>
          <p className="text-gray-600 mb-4">Voir les statistiques d'utilisation.</p>
          <a href="/fr/admin/statistiques" className="text-blue-600 hover:underline">Voir les stats →</a>
        </div>
      </div>
    </div>
  );
}