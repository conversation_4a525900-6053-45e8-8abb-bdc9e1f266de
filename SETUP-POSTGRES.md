# Configuration PostgreSQL avec Docker - LeClub

## 🐳 Prérequis Docker

Assurez-vous que Docker Desktop est installé et configuré pour WSL 2 :

1. **Installer Docker Desktop** : https://docs.docker.com/desktop/windows/install/
2. **Activer l'intégration WSL 2** dans Docker Desktop Settings → Resources → WSL Integration
3. **Redémarrer WSL** : `wsl --shutdown` puis rouvrir le terminal

## 🚀 Démarrage rapide

### 1. Lancer PostgreSQL avec Docker

```bash
# Démarrer PostgreSQL en arrière-plan
docker compose up -d postgres

# Ou utiliser le script npm
npm run docker:up
```

### 2. Vérifier que PostgreSQL fonctionne

```bash
# Vérifier les conteneurs
docker ps

# Voir les logs PostgreSQL
docker compose logs postgres
```

### 3. Configurer la base de données

```bash
# Générer le client Prisma
cd server && npx prisma generate

# Créer la première migration
npx prisma migrate dev --name init

# Alternative : push direct du schema
npx prisma db push
```

### 4. Démarrer l'application

```bash
# Retour à la racine
cd ..

# Démarrer le projet (démarre automatiquement Docker)
npm run dev
```

## 🗄️ Gestion de la base de données

### Commandes principales

```bash
# Migration de la base
npm run db:migrate

# Reset complet de la base
npm run db:reset

# Interface graphique Prisma Studio
npm run db:studio
```

### Commandes Docker

```bash
# Démarrer PostgreSQL
npm run docker:up

# Arrêter tous les conteneurs
npm run docker:down

# Reset complet (efface toutes les données)
npm run docker:reset

# Démarrer pgAdmin (interface web)
npm run docker:admin
```

## 🔧 Configuration avancée

### Variables d'environnement

Le fichier `server/.env` est déjà configuré pour Docker :

```env
DATABASE_URL="postgresql://leclub_user:leclub_password@localhost:5432/leclub_db"
```

### pgAdmin (Interface web optionnelle)

```bash
# Démarrer pgAdmin
npm run docker:admin

# Accéder à : http://localhost:5050
# Email : <EMAIL>
# Password : admin123
```

### Configuration serveur dans pgAdmin :
- **Host** : postgres (ou localhost si externe)
- **Port** : 5432
- **Database** : leclub_db
- **Username** : leclub_user
- **Password** : leclub_password

## 📊 Schema PostgreSQL

Le nouveau schéma utilise toutes les fonctionnalités PostgreSQL :

### ✅ Fonctionnalités restaurées
- **Enums typés** : UserRole, NewsCategory, AttendeeStatus, OrderStatus, JobType, NotificationType
- **JSON natif** : Business.hours, Notification.data
- **Arrays** : Business.images, Product.images
- **Decimal précis** : Product.price, Order.total, OrderItem.price
- **Extensions** : uuid-ossp, pg_trgm (recherche), unaccent (recherche française)

### 🗂️ Tables principales
- **users** : Utilisateurs avec rôles (RESIDENT, BUSINESS_OWNER, ADMIN)
- **businesses** : Entreprises avec profils riches
- **products** : Marketplace avec prix précis
- **news** : Actualités avec catégories
- **events** : Événements avec participants
- **forum_posts/comments** : Forum communautaire
- **orders/order_items** : E-commerce complet
- **notifications** : Alertes temps réel

## 🐛 Dépannage

### Docker non trouvé
```bash
# Vérifier Docker Desktop
docker --version

# Si erreur WSL, redémarrer
wsl --shutdown
# Puis rouvrir terminal
```

### Erreur de connexion base
```bash
# Vérifier que PostgreSQL est démarré
docker ps | grep postgres

# Voir les logs d'erreur
docker compose logs postgres

# Reset complet si nécessaire
npm run docker:reset
```

### Erreur Prisma
```bash
# Régénérer le client
cd server && npx prisma generate

# Reset schema
npx prisma db push --force-reset
```

## 📈 Performance et production

### Optimisations incluses
- **Index automatiques** sur clés étrangères
- **Contraintes d'intégrité** complètes
- **Types optimisés** pour requêtes rapides

### Monitoring
- **Prisma Studio** : Interface graphique pour données
- **pgAdmin** : Administration PostgreSQL complète
- **Docker logs** : Surveillance temps réel

Cette configuration vous donne une base PostgreSQL complète, prête pour le développement et extensible vers la production !