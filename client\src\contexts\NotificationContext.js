'use client';

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import socketService, { 
  NotificationData, 
  EmergencyNotification,
  ForumPostNotification,
  ForumCommentNotification,
  OrderUpdateNotification,
  AnnouncementNotification,
  MessageNotification
} from '@/lib/socket';
import api from '@/lib/api';

interface NotificationState {
  notifications: NotificationData[];
  unreadCount: number;
  isConnected: boolean;
  lastUpdate: string | null;
}

type NotificationAction =
  | { type: 'SET_NOTIFICATIONS'; payload: NotificationData[] }
  | { type: 'ADD_NOTIFICATION'; payload: NotificationData }
  | { type: 'MARK_READ'; payload: string }
  | { type: 'MARK_ALL_READ' }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'SET_CONNECTION_STATUS'; payload: boolean }
  | { type: 'UPDATE_UNREAD_COUNT'; payload: number };

interface NotificationContextType {
  notifications: NotificationData[];
  unreadCount: number;
  isConnected: boolean;
  lastUpdate: string | null;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  removeNotification: (notificationId: string) => void;
  addNotification: (notification: Omit<NotificationData, 'id' | 'timestamp' | 'read'>) => void;
  joinCommunity: (communityId: string) => void;
  leaveCommunity: (communityId: string) => void;
  joinForum: (forumId: string) => void;
  leaveForum: (forumId: string) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  isConnected: false,
  lastUpdate: null
};

function notificationReducer(state: NotificationState, action: NotificationAction): NotificationState {
  switch (action.type) {
    case 'SET_NOTIFICATIONS':
      const unreadCount = action.payload.filter(n => !n.read).length;
      return {
        ...state,
        notifications: action.payload,
        unreadCount,
        lastUpdate: new Date().toISOString()
      };

    case 'ADD_NOTIFICATION':
      const newNotifications = [action.payload, ...state.notifications].slice(0, 100); // Keep only 100 latest
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: action.payload.read ? state.unreadCount : state.unreadCount + 1,
        lastUpdate: new Date().toISOString()
      };

    case 'MARK_READ':
      const updatedNotifications = state.notifications.map(n =>
        n.id === action.payload ? { ...n, read: true } : n
      );
      const wasUnread = state.notifications.find(n => n.id === action.payload && !n.read);
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: wasUnread ? state.unreadCount - 1 : state.unreadCount,
        lastUpdate: new Date().toISOString()
      };

    case 'MARK_ALL_READ':
      return {
        ...state,
        notifications: state.notifications.map(n => ({ ...n, read: true })),
        unreadCount: 0,
        lastUpdate: new Date().toISOString()
      };

    case 'REMOVE_NOTIFICATION':
      const filteredNotifications = state.notifications.filter(n => n.id !== action.payload);
      const removedNotification = state.notifications.find(n => n.id === action.payload);
      return {
        ...state,
        notifications: filteredNotifications,
        unreadCount: removedNotification && !removedNotification.read 
          ? state.unreadCount - 1 
          : state.unreadCount,
        lastUpdate: new Date().toISOString()
      };

    case 'SET_CONNECTION_STATUS':
      return {
        ...state,
        isConnected: action.payload
      };

    case 'UPDATE_UNREAD_COUNT':
      return {
        ...state,
        unreadCount: action.payload
      };

    default:
      return state;
  }
}

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(notificationReducer, initialState);
  const { user, isAuthenticated, getToken } = useAuth();

  // Load initial notifications
  const loadNotifications = useCallback(async () => {
    if (!isAuthenticated || !user) return;

    try {
      const response = await api.getUserNotifications();
      if (response.success && response.data) {
        dispatch({ type: 'SET_NOTIFICATIONS', payload: response.data as NotificationData[] });
      }
    } catch (error) {
      console.error('Failed to load notifications:', error);
    }
  }, [isAuthenticated, user]);

  // Setup socket connection
  useEffect(() => {
    if (!isAuthenticated || !user) {
      socketService.disconnect();
      dispatch({ type: 'SET_CONNECTION_STATUS', payload: false });
      return;
    }

    const token = getToken();
    if (!token) return;

    // Connect to socket server
    const socket = socketService.connect(user.id, token);

    // Setup event listeners
    const handleNotification = (data: NotificationData) => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: data });
      
      // Show browser notification if permission granted
      if (Notification.permission === 'granted') {
        new Notification(data.title, {
          body: data.message,
          icon: '/favicon.ico',
          tag: data.id
        });
      }
    };

    const handleEmergencyNotification = (data: EmergencyNotification) => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: data });
      
      // Emergency notifications always show browser notification
      if (Notification.permission === 'granted') {
        new Notification(`🚨 ${data.title}`, {
          body: data.message,
          icon: '/favicon.ico',
          tag: data.id,
          requireInteraction: true // Keep notification until user interacts
        });
      }
    };

    const handleForumPost = (data: ForumPostNotification) => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: data });
    };

    const handleForumComment = (data: ForumCommentNotification) => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: data });
    };

    const handleOrderUpdate = (data: OrderUpdateNotification) => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: data });
    };

    const handleAnnouncement = (data: AnnouncementNotification) => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: data });
    };

    const handleMessage = (data: MessageNotification) => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: data });
    };

    // Add event listeners
    socketService.onNotification(handleNotification);
    socketService.onEmergencyNotification(handleEmergencyNotification);
    socketService.onForumPost(handleForumPost);
    socketService.onForumComment(handleForumComment);
    socketService.onOrderUpdate(handleOrderUpdate);
    socketService.onAnnouncement(handleAnnouncement);
    socketService.onMessage(handleMessage);

    // Handle connection status
    socket.on('connect', () => {
      dispatch({ type: 'SET_CONNECTION_STATUS', payload: true });
      loadNotifications(); // Reload notifications on reconnect
    });

    socket.on('disconnect', () => {
      dispatch({ type: 'SET_CONNECTION_STATUS', payload: false });
    });

    // Auto-join user's community if available
    if (user.communityId) {
      socketService.joinCommunity(user.communityId);
    }

    // Cleanup on unmount
    return () => {
      socketService.offNotification(handleNotification);
      socketService.offEmergencyNotification(handleEmergencyNotification);
      socketService.offForumPost(handleForumPost);
      socketService.offForumComment(handleForumComment);
      socketService.offOrderUpdate(handleOrderUpdate);
      socketService.offAnnouncement(handleAnnouncement);
      socketService.offMessage(handleMessage);
      socketService.disconnect();
    };
  }, [isAuthenticated, user, getToken, loadNotifications]);

  // Load notifications on mount
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // Request browser notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  const markAsRead = useCallback(async (notificationId: string) => {
    dispatch({ type: 'MARK_READ', payload: notificationId });
    socketService.markNotificationRead(notificationId);
    
    try {
      await api.markNotificationRead(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    dispatch({ type: 'MARK_ALL_READ' });
    socketService.markAllNotificationsRead();
    
    try {
      await api.markAllNotificationsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, []);

  const removeNotification = useCallback((notificationId: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: notificationId });
  }, []);

  const addNotification = useCallback((notification: Omit<NotificationData, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: NotificationData = {
      ...notification,
      id: `local-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      read: false
    };
    dispatch({ type: 'ADD_NOTIFICATION', payload: newNotification });
  }, []);

  const joinCommunity = useCallback((communityId: string) => {
    socketService.joinCommunity(communityId);
  }, []);

  const leaveCommunity = useCallback((communityId: string) => {
    socketService.leaveCommunity(communityId);
  }, []);

  const joinForum = useCallback((forumId: string) => {
    socketService.joinForum(forumId);
  }, []);

  const leaveForum = useCallback((forumId: string) => {
    socketService.leaveForum(forumId);
  }, []);

  const value: NotificationContextType = {
    notifications: state.notifications,
    unreadCount: state.unreadCount,
    isConnected: state.isConnected,
    lastUpdate: state.lastUpdate,
    markAsRead,
    markAllAsRead,
    removeNotification,
    addNotification,
    joinCommunity,
    leaveCommunity,
    joinForum,
    leaveForum
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

export default NotificationContext;