{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm init:*)", "<PERSON><PERSON>(docker run:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(node:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(npm install:*)", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(DATABASE_URL=\"postgresql://leclub_user:leclub_password@localhost:5432/leclub_db\" node test-connection.js)", "<PERSON><PERSON>(docker-compose down:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(npm run docker:up:*)", "Bash(npx prisma migrate dev:*)", "Bash(PGPASSWORD=leclub_password psql -h localhost -U leclub_user -d leclub_db -c \"SELECT version();\")", "Bash(PGPASSWORD=leclub_password psql -h localhost -U leclub_user -d leclub_db -c \"SELECT version();\")", "Bash(sudo systemctl stop:*)", "Bash(PGPASSWORD=leclub_password psql -h localhost -p 5433 -U leclub_user -d leclub_db -c \"SELECT version();\")", "Bash(psql:*)", "Bash(PGPASSWORD=password psql:*)", "Bash(ls:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm run build:*)", "Bash(npm --version)", "Bash(rm:*)", "Bash(npm audit:*)", "Bash(npm run type-check:*)", "Bash(npx next dev:*)", "<PERSON><PERSON>(hostname:*)", "mcp__playwright__browser_navigate", "Bash(kill:*)", "Bash(npx ts-node:*)", "Bash(npm run docker:clean:*)", "Bash(npm start)", "Bash(git branch:*)", "Bash(git rm:*)", "Bash(git commit:*)", "Bash(git config:*)", "Bash(gh repo create:*)", "Bash(git remote:*)", "Bash(git push:*)", "Bash(grep:*)", "<PERSON><PERSON>(true)", "Bash(git add:*)", "mcp__playwright__browser_click", "Bash(npm ls:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(cat:*)", "Bash(yarn install)", "Bash(yarn dev:client:*)", "Bash(PORT=3004 npm run dev)", "<PERSON><PERSON>(timeout:*)", "Bash(PORT=3009 npm run dev)", "Bash(sudo lsof:*)", "<PERSON><PERSON>(mv:*)"], "deny": []}}