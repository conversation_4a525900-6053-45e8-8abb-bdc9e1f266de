import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Hash password for demo users
  const hashedPassword = await bcrypt.hash('password123', 10);

  // Create admin user
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'LeClub',
      role: 'ADMIN',
      isVerified: true,
      bio: 'Administrateur de la plateforme LeClub'
    }
  });

  // Create business owner
  const businessOwner = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      role: 'BUSINESS_OWNER',
      isVerified: true,
      phone: '555-0123',
      address: '12 Rue du Commerce',
      bio: '<PERSON><PERSON><PERSON><PERSON><PERSON> de la Boulangerie Artisanale Martin'
    }
  });

  // Create regular residents
  const resident1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Jean',
      lastName: 'Dupont',
      role: 'RESIDENT',
      isVerified: true,
      address: '25 Avenue Principale'
    }
  });

  const resident2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Sophie',
      lastName: 'Laurent',
      role: 'RESIDENT',
      isVerified: true,
      address: '8 Rue des Fleurs'
    }
  });

  // Create businesses
  const boulangerie = await prisma.business.upsert({
    where: { id: 'boulangerie-martin' },
    update: {},
    create: {
      id: 'boulangerie-martin',
      name: 'Boulangerie Artisanale Martin',
      description: 'Pain frais, viennoiseries et pâtisseries fait maison depuis 1985. Spécialités : pain de campagne, croissants au beurre, tartes aux fruits de saison.',
      category: 'Alimentation',
      address: '12 Rue du Commerce',
      phone: '555-0123',
      email: '<EMAIL>',
      website: 'https://boulangerie-martin.local',
      hours: {
        lundi: { ouvert: true, heures: '7h00-19h00' },
        mardi: { ouvert: true, heures: '7h00-19h00' },
        mercredi: { ouvert: true, heures: '7h00-19h00' },
        jeudi: { ouvert: true, heures: '7h00-19h00' },
        vendredi: { ouvert: true, heures: '7h00-19h00' },
        samedi: { ouvert: true, heures: '7h00-18h00' },
        dimanche: { ouvert: true, heures: '8h00-13h00' }
      },
      images: ['/images/boulangerie-facade.jpg', '/images/boulangerie-interieur.jpg'],
      isVerified: true,
      ownerId: businessOwner.id
    }
  });

  const cafeDesAmis = await prisma.business.upsert({
    where: { id: 'cafe-des-amis' },
    update: {},
    create: {
      id: 'cafe-des-amis',
      name: 'Café des Amis',
      description: 'Café de spécialité, brunch et espace de coworking chaleureux. WiFi gratuit, terrasse ensoleillée.',
      category: 'Restaurant',
      address: '5 Place Centrale',
      phone: '555-0456',
      email: '<EMAIL>',
      hours: {
        lundi: { ouvert: true, heures: '8h00-22h00' },
        mardi: { ouvert: true, heures: '8h00-22h00' },
        mercredi: { ouvert: true, heures: '8h00-22h00' },
        jeudi: { ouvert: true, heures: '8h00-22h00' },
        vendredi: { ouvert: true, heures: '8h00-23h00' },
        samedi: { ouvert: true, heures: '9h00-23h00' },
        dimanche: { ouvert: true, heures: '9h00-21h00' }
      },
      images: ['/images/cafe-terrasse.jpg'],
      isVerified: true,
      ownerId: businessOwner.id
    }
  });

  // Create products
  await prisma.product.createMany({
    data: [
      {
        name: 'Pain de campagne',
        description: 'Pain artisanal au levain, cuit au feu de bois. Croûte dorée et mie alvéolée.',
        price: 3.50,
        category: 'Boulangerie',
        images: ['/images/pain-campagne.jpg'],
        stock: 20,
        sellerId: businessOwner.id,
        businessId: boulangerie.id
      },
      {
        name: 'Croissants au beurre (x6)',
        description: 'Croissants artisanaux au beurre AOP, feuilletage parfait.',
        price: 8.90,
        category: 'Viennoiserie',
        images: ['/images/croissants.jpg'],
        stock: 15,
        sellerId: businessOwner.id,
        businessId: boulangerie.id
      },
      {
        name: 'Tarte aux pommes',
        description: 'Tarte aux pommes locales, pâte brisée maison, crème pâtissière.',
        price: 18.00,
        category: 'Pâtisserie',
        images: ['/images/tarte-pommes.jpg'],
        stock: 5,
        sellerId: businessOwner.id,
        businessId: boulangerie.id
      }
    ]
  });

  // Create forum categories
  await prisma.forumCategory.createMany({
    data: [
      {
        name: 'Annonces officielles',
        description: 'Communications officielles de la mairie',
        color: '#3B82F6',
        order: 1
      },
      {
        name: 'Vie locale',
        description: 'Discussions sur la vie quotidienne du village',
        color: '#10B981',
        order: 2
      },
      {
        name: 'Événements',
        description: 'Organisation et discussion des événements locaux',
        color: '#F59E0B',
        order: 3
      },
      {
        name: 'Entraide',
        description: 'Demandes d\'aide et services entre voisins',
        color: '#EF4444',
        order: 4
      },
      {
        name: 'Commerce local',
        description: 'Discussions sur les commerces et artisans locaux',
        color: '#8B5CF6',
        order: 5
      }
    ]
  });

  // Create news articles
  const vieLocaleCategory = await prisma.forumCategory.findFirst({
    where: { name: 'Vie locale' }
  });

  await prisma.news.createMany({
    data: [
      {
        title: 'Nouveau parc communautaire inauguré',
        content: 'Le nouveau parc familial avec aires de jeux, espaces verts et parcours de santé a été officiellement inauguré ce weekend. Situé avenue des Tilleuls, il dispose d\'équipements modernes pour tous les âges et d\'un espace pique-nique couvert.',
        excerpt: 'Le nouveau parc familial avec aires de jeux et espaces verts ouvre ses portes ce weekend.',
        category: 'COMMUNITY',
        isPublished: true,
        publishedAt: new Date('2024-01-15'),
        authorId: admin.id
      },
      {
        title: 'Marché fermier hebdomadaire',
        content: 'Tous les samedis matins de 8h à 13h, place centrale. Découvrez les produits frais de nos producteurs locaux : légumes de saison, fromages fermiers, miel local, pain bio et bien plus encore. Une initiative pour soutenir l\'agriculture locale et offrir des produits de qualité aux résidents.',
        excerpt: 'Tous les samedis matins, découvrez les produits frais de nos producteurs locaux.',
        category: 'EVENTS',
        isPublished: true,
        publishedAt: new Date('2024-01-14'),
        authorId: admin.id
      },
      {
        title: 'Travaux de réfection - Rue Principale',
        content: 'Des travaux de réfection de la chaussée auront lieu rue Principale du 20 au 30 janvier. Une circulation alternée sera mise en place avec feux temporaires. Les riverains sont priés de stationner leurs véhicules dans les rues adjacentes pendant cette période.',
        excerpt: 'Des travaux de réfection auront lieu du 20 au 30 janvier. Circulation alternée prévue.',
        category: 'OFFICIAL',
        isPublished: true,
        publishedAt: new Date('2024-01-13'),
        authorId: admin.id
      }
    ]
  });

  // Create events
  await prisma.event.createMany({
    data: [
      {
        title: 'Festival d\'automne',
        description: 'Célébrons ensemble les couleurs d\'automne avec un programme riche : concerts en plein air, marché artisanal, dégustations de produits locaux, animations pour enfants et feu d\'artifice en soirée.',
        location: 'Place du Village',
        date: new Date('2024-10-15T14:00:00'),
        endDate: new Date('2024-10-15T22:00:00'),
        category: 'Culturel',
        maxAttendees: 500,
        organizerId: admin.id
      },
      {
        title: 'Atelier jardinage communautaire',
        description: 'Apprenez les techniques de jardinage écologique avec nos experts locaux. Au programme : compostage, permaculture, plants et graines gratuits pour démarrer votre potager.',
        location: 'Jardin communautaire, rue des Jardins',
        date: new Date('2024-02-10T10:00:00'),
        endDate: new Date('2024-02-10T16:00:00'),
        category: 'Éducation',
        maxAttendees: 30,
        organizerId: admin.id
      },
      {
        title: 'Réunion conseil municipal',
        description: 'Ordre du jour : budget 2024, nouveaux projets d\'infrastructure, amélioration des espaces verts, questions diverses des citoyens.',
        location: 'Mairie - Salle du conseil',
        date: new Date('2024-02-08T19:30:00'),
        endDate: new Date('2024-02-08T21:30:00'),
        category: 'Civique',
        organizerId: admin.id
      }
    ]
  });

  // Create job postings
  await prisma.jobPosting.createMany({
    data: [
      {
        title: 'Vendeur/Vendeuse en boulangerie',
        description: 'Recherchons vendeur(se) dynamique pour notre boulangerie. Expérience souhaitée mais formation possible. Horaires : 6h-14h ou 14h-19h. Contact accueil, conseil clientèle, encaissement.',
        type: 'PART_TIME',
        salary: '11€/heure + primes',
        location: 'Boulangerie Martin',
        requirements: 'Sens du contact, disponibilité weekend, hygiène irréprochable',
        employerId: businessOwner.id,
        businessId: boulangerie.id
      },
      {
        title: 'Agent d\'entretien espaces verts',
        description: 'La mairie recrute un agent pour l\'entretien des espaces verts communaux. Mission : tonte, taille, plantations, entretien des équipements. Permis B requis.',
        type: 'FULL_TIME',
        salary: 'Selon grille territoriale',
        location: 'Services techniques municipaux',
        requirements: 'Expérience espaces verts, permis B, aptitude physique',
        employerId: admin.id
      }
    ]
  });

  console.log('✅ Database seeded successfully!');
  console.log(`👤 Admin user: <EMAIL> (password: password123)`);
  console.log(`🏪 Business owner: <EMAIL> (password: password123)`);
  console.log(`👥 Residents: <EMAIL>, <EMAIL> (password: password123)`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });