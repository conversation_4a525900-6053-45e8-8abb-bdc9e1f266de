{"name": "leclub-server", "version": "1.0.0", "description": "API backend pour la plateforme communautaire LeClub", "main": "dist/index.js", "scripts": {"dev": "nodemon -r tsconfig-paths/register src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "seed": "ts-node src/prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pg": "^8.16.1", "prisma": "^5.7.1", "socket.io": "^4.7.4", "stripe": "^14.9.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^22.9.0", "@types/nodemailer": "^6.4.14", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.57.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "keywords": ["api", "communauté", "marché-local", "express", "typescript"], "author": "", "license": "MIT"}