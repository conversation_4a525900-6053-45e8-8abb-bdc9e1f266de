import express from 'express';
import { body } from 'express-validator';
import {
  getEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  rsvpEvent,
  getEventAttendees
} from '../controllers/eventsController';
import { protect, authorize } from '../middleware/auth';

const router = express.Router();

// Public routes
router.get('/', getEvents);
router.get('/:id', getEventById);
router.get('/:id/attendees', getEventAttendees);

// Protected routes - Authenticated users can create events
router.post('/', protect, [
  body('title').notEmpty().withMessage('Le titre est requis'),
  body('description').notEmpty().withMessage('La description est requise'),
  body('location').notEmpty().withMessage('Le lieu est requis'),
  body('date').isISO8601().withMessage('Date invalide'),
  body('endDate').optional().isISO8601().withMessage('Date de fin invalide'),
  body('category').notEmpty().withMessage('La catégorie est requise'),
  body('maxAttendees').optional().isInt({ min: 1 }).withMessage('Nombre maximum de participants invalide'),
  body('isPublic').optional().isBoolean().withMessage('Visibilité invalide')
], createEvent);

// Protected routes - Only organizer or admin can modify
router.put('/:id', protect, [
  body('title').optional().notEmpty().withMessage('Le titre ne peut pas être vide'),
  body('description').optional().notEmpty().withMessage('La description ne peut pas être vide'),
  body('location').optional().notEmpty().withMessage('Le lieu ne peut pas être vide'),
  body('date').optional().isISO8601().withMessage('Date invalide'),
  body('endDate').optional().isISO8601().withMessage('Date de fin invalide'),
  body('category').optional().notEmpty().withMessage('La catégorie ne peut pas être vide'),
  body('maxAttendees').optional().isInt({ min: 1 }).withMessage('Nombre maximum de participants invalide'),
  body('isPublic').optional().isBoolean().withMessage('Visibilité invalide')
], updateEvent);

router.delete('/:id', protect, deleteEvent);

// RSVP routes
router.post('/:id/rsvp', protect, [
  body('status').isIn(['INTERESTED', 'ATTENDING', 'NOT_ATTENDING'])
    .withMessage('Statut RSVP invalide')
], rsvpEvent);

export default router;