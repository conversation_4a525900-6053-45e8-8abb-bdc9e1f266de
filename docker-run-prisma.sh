#!/bin/bash
# Run Prisma commands from within Docker network

# Create a temporary container with Node.js and run Prisma commands
docker run --rm -v "$(pwd)":/app -w /app --network leclub_leclub-network \
  -e DATABASE_URL="******************************************************/leclub_db" \
  node:18-alpine sh -c "
    cd server && \
    npm install -g prisma && \
    npx prisma db push && \
    npx prisma db seed
  "