const { PrismaClient } = require('@prisma/client');

async function testConnection() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Testing database connection...');
    const users = await prisma.user.count();
    console.log(`✅ Connected! Found ${users} users in database`);
    
    // Test a simple query
    const admin = await prisma.user.findFirst({ where: { role: 'ADMIN' } });
    console.log(`👤 Admin user: ${admin?.email}`);
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();