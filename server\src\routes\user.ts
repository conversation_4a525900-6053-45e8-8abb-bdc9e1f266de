import express from 'express';
import { protect } from '../middleware/auth';

const router = express.Router();

// Placeholder routes - to be implemented
router.get('/notifications', protect, (req, res) => {
  res.json({ message: 'User notifications - À implémenter' });
});

router.get('/orders', protect, (req, res) => {
  res.json({ message: 'User orders - À implémenter' });
});

router.get('/reviews', protect, (req, res) => {
  res.json({ message: 'User reviews - À implémenter' });
});

export default router;