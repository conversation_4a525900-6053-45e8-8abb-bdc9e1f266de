# Docker Environment Variables for LeClub

# Database Configuration (Docker)
DATABASE_URL=******************************************************/leclub_db

# JWT Configuration
JWT_SECRET=leclub-super-secret-jwt-key-change-this-in-production-2024
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration (Docker)
CORS_ORIGIN=http://localhost:3000

# Client Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_DEFAULT_LOCALE=fr

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Stripe Configuration (for payments)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# File Upload Configuration
MAX_FILE_SIZE=5MB
UPLOAD_DIR=uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100