export default function HomePage() {
  return (
    <div>
      <h1 className="text-4xl font-bold mb-6">Bienvenue sur LeClub</h1>
      <p className="text-xl text-gray-600 mb-8">Votre Communauté, Votre Marché Local</p>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">📰 Actualités</h2>
          <p className="text-gray-600">Restez informé des dernières nouvelles de votre communauté.</p>
          <a href="/fr/actualites" className="text-blue-600 hover:underline">Voir les actualités →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">📅 Événements</h2>
          <p className="text-gray-600">Découvrez les événements à venir dans votre région.</p>
          <a href="/fr/evenements" className="text-blue-600 hover:underline">Voir les événements →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">💼 Emplois</h2>
          <p className="text-gray-600">Trouvez des opportunités d'emploi locales.</p>
          <a href="/fr/emplois" className="text-blue-600 hover:underline">Voir les emplois →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">🏪 Marché</h2>
          <p className="text-gray-600">Achetez et vendez des produits locaux.</p>
          <a href="/fr/marche" className="text-blue-600 hover:underline">Voir le marché →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">🏢 Entreprises</h2>
          <p className="text-gray-600">Répertoire des entreprises locales.</p>
          <a href="/fr/entreprises" className="text-blue-600 hover:underline">Voir les entreprises →</a>
        </div>
        
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">💬 Forum</h2>
          <p className="text-gray-600">Participez aux discussions communautaires.</p>
          <a href="/fr/forum" className="text-blue-600 hover:underline">Voir le forum →</a>
        </div>
      </div>
    </div>
  );
}