'use client';

import React, { useState, useEffect } from 'react';
import { X, CheckCircle, AlertTriangle, Info, XCircle, Bell } from 'lucide-react';
import { useNotifications } from '@/contexts/NotificationContext';
import { NotificationData } from '@/lib/socket';
import Link from 'next/link';

interface ToastNotification extends NotificationData {
  show: boolean;
  removing: boolean;
}

interface NotificationToastProps {
  className?: string;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  maxToasts?: number;
  autoHideDuration?: number;
}

export default function NotificationToast({ 
  className = '',
  position = 'top-right',
  maxToasts = 3,
  autoHideDuration = 5000
}: NotificationToastProps) {
  const { notifications } = useNotifications();
  const [toasts, setToasts] = useState<ToastNotification[]>([]);

  // Add new notifications as toasts
  useEffect(() => {
    const latestNotification = notifications[0];
    if (!latestNotification) return;

    // Don't show emergency notifications as toasts (they have their own banner)
    if (latestNotification.type === 'emergency') return;

    // Check if this notification is already shown as a toast
    const alreadyShown = toasts.some(toast => toast.id === latestNotification.id);
    if (alreadyShown) return;

    const newToast: ToastNotification = {
      ...latestNotification,
      show: true,
      removing: false
    };

    setToasts(prev => {
      const updatedToasts = [newToast, ...prev];
      // Keep only the max number of toasts
      return updatedToasts.slice(0, maxToasts);
    });
  }, [notifications, maxToasts, toasts]);

  // Auto-hide toasts
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];

    toasts.forEach((toast, index) => {
      if (toast.show && !toast.removing) {
        const timer = setTimeout(() => {
          removeToast(toast.id);
        }, autoHideDuration + (index * 100)); // Stagger removal slightly
        timers.push(timer);
      }
    });

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [toasts, autoHideDuration]);

  const removeToast = (toastId: string) => {
    setToasts(prev =>
      prev.map(toast =>
        toast.id === toastId
          ? { ...toast, removing: true }
          : toast
      )
    );

    // Remove from DOM after animation
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== toastId));
    }, 300);
  };

  const getToastIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle size={20} className="text-green-600" />;
      case 'error':
        return <XCircle size={20} className="text-red-600" />;
      case 'warning':
        return <AlertTriangle size={20} className="text-orange-600" />;
      case 'info':
        return <Info size={20} className="text-blue-600" />;
      default:
        return <Bell size={20} className="text-gray-600" />;
    }
  };

  const getToastBorderColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-l-green-500';
      case 'error':
        return 'border-l-red-500';
      case 'warning':
        return 'border-l-orange-500';
      case 'info':
        return 'border-l-blue-500';
      default:
        return 'border-l-gray-500';
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 right-4';
    }
  };

  if (toasts.length === 0) {
    return null;
  }

  return (
    <div className={`fixed ${getPositionClasses()} z-50 space-y-2 pointer-events-none ${className}`}>
      {toasts.map((toast, index) => (
        <div
          key={toast.id}
          className={`
            pointer-events-auto transform transition-all duration-300 ease-in-out
            ${toast.removing 
              ? 'translate-x-full opacity-0 scale-95' 
              : toast.show 
                ? 'translate-x-0 opacity-100 scale-100' 
                : 'translate-x-full opacity-0 scale-95'
            }
          `}
          style={{ 
            animationDelay: `${index * 100}ms`,
            marginTop: index > 0 ? '8px' : '0'
          }}
        >
          <div className={`
            bg-white rounded-lg shadow-lg border-l-4 ${getToastBorderColor(toast.type)}
            max-w-sm p-4 flex items-start space-x-3
            hover:shadow-xl transition-shadow duration-200
          `}>
            {/* Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {getToastIcon(toast.type)}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-semibold text-gray-900 leading-tight">
                {toast.title}
              </h4>
              <p className="text-sm text-gray-600 mt-1 leading-relaxed">
                {toast.message}
              </p>
              
              {/* Action button */}
              {toast.actionUrl && toast.actionText && (
                <Link
                  href={toast.actionUrl}
                  className="inline-block mt-2 text-xs text-primary-600 hover:text-primary-700 font-medium"
                  onClick={() => removeToast(toast.id)}
                >
                  {toast.actionText} →
                </Link>
              )}
              
              {/* Timestamp */}
              <p className="text-xs text-gray-500 mt-2">
                {new Date(toast.timestamp).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>

            {/* Close button */}
            <button
              onClick={() => removeToast(toast.id)}
              className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Fermer"
            >
              <X size={16} />
            </button>
          </div>

          {/* Progress bar for auto-hide */}
          <div className="h-1 bg-gray-200 rounded-b-lg overflow-hidden">
            <div 
              className={`h-full ${
                toast.type === 'success' ? 'bg-green-500' :
                toast.type === 'error' ? 'bg-red-500' :
                toast.type === 'warning' ? 'bg-orange-500' :
                toast.type === 'info' ? 'bg-blue-500' :
                'bg-gray-500'
              } transition-all duration-${autoHideDuration} ease-linear`}
              style={{
                width: '100%',
                animation: `shrink ${autoHideDuration}ms linear forwards`
              }}
            />
          </div>
        </div>
      ))}

      {/* Custom CSS for progress bar animation */}
      <style>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
}