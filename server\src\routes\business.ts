import express from 'express';
import { body } from 'express-validator';
import {
  getBusinesses,
  getBusinessById,
  createBusiness,
  updateBusiness,
  deleteBusiness,
  getBusinessReviews,
  addReview,
  updateReview,
  deleteReview,
  getBusinessCategories
} from '../controllers/businessController';
import { protect, authorize } from '../middleware/auth';

const router = express.Router();

// Public routes
router.get('/', getBusinesses);
router.get('/categories', getBusinessCategories);
router.get('/:id', getBusinessById);
router.get('/:id/reviews', getBusinessReviews);

// Protected routes - Authenticated users can create businesses
router.post('/', protect, [
  body('name').notEmpty().withMessage('Le nom de l\'entreprise est requis'),
  body('description').optional().isLength({ max: 1000 }).withMessage('La description ne peut pas dépasser 1000 caractères'),
  body('category').notEmpty().withMessage('La catégorie est requise'),
  body('address').notEmpty().withMessage('L\'adresse est requise'),
  body('phone').optional().isMobilePhone('fr-FR').withMessage('Numéro de téléphone invalide'),
  body('email').optional().isEmail().withMessage('Email invalide'),
  body('website').optional().isURL().withMessage('URL du site web invalide')
], createBusiness);

// Protected routes - Owner or admin can modify
router.put('/:id', protect, [
  body('name').optional().notEmpty().withMessage('Le nom ne peut pas être vide'),
  body('description').optional().isLength({ max: 1000 }).withMessage('La description ne peut pas dépasser 1000 caractères'),
  body('category').optional().notEmpty().withMessage('La catégorie ne peut pas être vide'),
  body('address').optional().notEmpty().withMessage('L\'adresse ne peut pas être vide'),
  body('phone').optional().isMobilePhone('fr-FR').withMessage('Numéro de téléphone invalide'),
  body('email').optional().isEmail().withMessage('Email invalide'),
  body('website').optional().isURL().withMessage('URL du site web invalide')
], updateBusiness);

router.delete('/:id', protect, deleteBusiness);

// Review routes
router.post('/:id/reviews', protect, [
  body('rating').isInt({ min: 1, max: 5 }).withMessage('La note doit être entre 1 et 5'),
  body('comment').optional().isLength({ max: 500 }).withMessage('Le commentaire ne peut pas dépasser 500 caractères')
], addReview);

router.put('/reviews/:reviewId', protect, [
  body('rating').optional().isInt({ min: 1, max: 5 }).withMessage('La note doit être entre 1 et 5'),
  body('comment').optional().isLength({ max: 500 }).withMessage('Le commentaire ne peut pas dépasser 500 caractères')
], updateReview);

router.delete('/reviews/:reviewId', protect, deleteReview);

export default router;