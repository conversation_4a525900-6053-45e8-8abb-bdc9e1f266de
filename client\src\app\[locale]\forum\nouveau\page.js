export default function NouveauSujetPage() {
  return (
    <div>
      <div className="mb-6">
        <a href="/fr/forum" className="text-blue-600 hover:underline">
          ← Retour au forum
        </a>
      </div>
      
      <h1 className="text-3xl font-bold mb-6">Créer un nouveau sujet</h1>
      
      <form className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Catégorie</label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Choisir une catégorie</option>
            <option value="general">Discussions générales</option>
            <option value="entraide">Entraide</option>
            <option value="evenements">Événements</option>
            <option value="commerces">Commerces locaux</option>
            <option value="suggestions">Suggestions</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Titre du sujet</label>
          <input
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Donnez un titre clair à votre sujet"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Contenu</label>
          <textarea
            rows={8}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Décrivez votre sujet en détail..."
            required
          ></textarea>
        </div>
        
        <div className="flex space-x-4">
          <button
            type="submit"
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Publier le sujet
          </button>
          
          <button
            type="button"
            className="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 transition-colors"
          >
            Annuler
          </button>
        </div>
      </form>
    </div>
  );
}