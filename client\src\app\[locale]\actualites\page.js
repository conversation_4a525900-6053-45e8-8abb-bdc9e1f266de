// Server Component with async data fetching
async function getActualites() {
  // Simulate API call with proper server-side data fetching
  await new <PERSON>(resolve => setTimeout(resolve, 100)); // Small delay to show loading works
  
  return [
    {
      id: 1,
      title: "Nouvelle aire de jeux inaugurée au parc central",
      content: "La municipalité a inauguré une nouvelle aire de jeux moderne équipée de structures adaptées à tous les âges.",
      type: "Officiel",
      date: "15 janvier 2024"
    },
    {
      id: 2, 
      title: "Festival de musique locale ce weekend",
      content: "Rejoignez-nous pour un weekend musical avec les artistes locaux sur la place du marché.",
      type: "Communauté",
      date: "14 janvier 2024"
    },
    {
      id: 3,
      title: "Nouvelles mesures de circulation en centre-ville", 
      content: "À partir du 1er février, de nouvelles restrictions de circulation seront mises en place.",
      type: "Officiel",
      date: "13 janvier 2024"
    }
  ];
}

export default async function ActualitesPage() {
  // Server-side data fetching - runs on server, not client
  const actualites = await getActualites();
  
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Actualités & Annonces</h1>
      
      <div className="grid gap-6">
        {actualites.map((actualite) => (
          <article key={actualite.id} className="border p-6 rounded-lg hover:shadow-md transition-shadow">
            <h2 className="text-xl font-semibold mb-2">{actualite.title}</h2>
            <p className="text-gray-600 mb-4">{actualite.content}</p>
            <span className={`text-sm ${actualite.type === 'Officiel' ? 'text-blue-600' : 'text-green-600'}`}>
              {actualite.type} • {actualite.date}
            </span>
          </article>
        ))}
      </div>
    </div>
  );
}