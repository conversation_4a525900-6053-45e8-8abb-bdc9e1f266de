import { Business, Event, User } from './types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: any[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

class ApiClient {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    // Get token from localStorage if available
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      // Handle different content types
      let data;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = { message: await response.text() };
      }

      if (!response.ok) {
        throw new ApiError(
          data.message || data.error || `HTTP error! status: ${response.status}`,
          response.status,
          data.code,
          data.details || data.errors
        );
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      
      // Re-throw ApiError as-is
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Handle network errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new ApiError(
          'Erreur de connexion au serveur. Vérifiez votre connexion internet.',
          0,
          'NETWORK_ERROR'
        );
      }
      
      // Handle other errors
      throw new ApiError(
        (error as Error)?.message || 'Une erreur inattendue est survenue',
        0,
        'UNKNOWN_ERROR'
      );
    }
  }

  // Generic HTTP methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint);
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }

  // Authentication
  async login(email: string, password: string) {
    const response = await this.post<{
      user: any;
      token: string;
    }>('/auth/login', { email, password });

    if (response.success && response.data?.token) {
      // Store token in localStorage
      localStorage.setItem('auth_token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }

    return response;
  }

  async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) {
    return this.post('/auth/register', userData);
  }

  async logout() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.get<User>('/auth/profile');
  }

  // Events API
  async getEvents(params?: {
    category?: string;
    upcoming?: boolean;
    limit?: number;
  }): Promise<ApiResponse<Event[]>> {
    const searchParams = new URLSearchParams();
    if (params?.category) searchParams.set('category', params.category);
    if (params?.upcoming) searchParams.set('upcoming', 'true');
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    
    const query = searchParams.toString();
    return this.get<Event[]>(`/events${query ? `?${query}` : ''}`);
  }

  async getEvent(id: string) {
    return this.get(`/events/${id}`);
  }

  async createEvent(eventData: any) {
    return this.post('/events', eventData);
  }

  async rsvpEvent(eventId: string, status: 'INTERESTED' | 'ATTENDING' | 'NOT_ATTENDING') {
    return this.post(`/events/${eventId}/rsvp`, { status });
  }

  async getEventAttendees(eventId: string) {
    return this.get(`/events/${eventId}/attendees`);
  }

  // Forum API
  async getForumCategories() {
    return this.get('/forum/categories');
  }

  async getForumPosts(params?: {
    categoryId?: string;
    page?: number;
    limit?: number;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params?.categoryId) searchParams.set('categoryId', params.categoryId);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);
    
    const query = searchParams.toString();
    return this.get(`/forum/posts${query ? `?${query}` : ''}`);
  }

  async getForumPost(id: string) {
    return this.get(`/forum/posts/${id}`);
  }

  async createForumPost(postData: {
    title: string;
    content: string;
    categoryId: string;
  }) {
    return this.post('/forum/posts', postData);
  }

  async addForumComment(postId: string, content: string) {
    return this.post(`/forum/posts/${postId}/comments`, { content });
  }

  // Business API
  async getBusinesses(params?: {
    category?: string;
    verified?: boolean;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<Business[]>> {
    const searchParams = new URLSearchParams();
    if (params?.category) searchParams.set('category', params.category);
    if (params?.verified) searchParams.set('verified', 'true');
    if (params?.search) searchParams.set('search', params.search);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    
    const query = searchParams.toString();
    return this.get<Business[]>(`/business${query ? `?${query}` : ''}`);
  }

  async getBusiness(id: string) {
    return this.get(`/business/${id}`);
  }

  async getBusinessCategories() {
    return this.get('/business/categories');
  }

  async addBusinessReview(businessId: string, rating: number, comment?: string) {
    return this.post(`/business/${businessId}/reviews`, { rating, comment });
  }

  // Marketplace API
  async getProducts(params?: {
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    inStock?: boolean;
    page?: number;
    limit?: number;
  }) {
    const searchParams = new URLSearchParams();
    if (params?.category) searchParams.set('category', params.category);
    if (params?.search) searchParams.set('search', params.search);
    if (params?.minPrice) searchParams.set('minPrice', params.minPrice.toString());
    if (params?.maxPrice) searchParams.set('maxPrice', params.maxPrice.toString());
    if (params?.inStock) searchParams.set('inStock', 'true');
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    
    const query = searchParams.toString();
    return this.get(`/marketplace/products${query ? `?${query}` : ''}`);
  }

  async getProduct(id: string) {
    return this.get(`/marketplace/products/${id}`);
  }

  async getProductCategories() {
    return this.get('/marketplace/products/categories');
  }

  async createOrder(orderData: {
    items: Array<{ productId: string; quantity: number }>;
    paymentMethod?: string;
    notes?: string;
  }) {
    return this.post('/marketplace/orders', orderData);
  }

  async getUserOrders() {
    return this.get('/marketplace/orders');
  }

  async getOrder(id: string) {
    return this.get(`/marketplace/orders/${id}`);
  }

  // News API
  async getNews(params?: {
    category?: string;
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params?.category) searchParams.set('category', params.category);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);
    if (params?.status) searchParams.set('status', params.status);
    
    const query = searchParams.toString();
    return this.get(`/news${query ? `?${query}` : ''}`);
  }

  async getNewsById(id: string) {
    return this.get(`/news/${id}`);
  }

  async createNews(newsData: any) {
    return this.post('/news', newsData);
  }

  async updateNews(id: string, newsData: any) {
    return this.put(`/news/${id}`, newsData);
  }

  async deleteNews(id: string) {
    return this.delete(`/news/${id}`);
  }

  // Enhanced Event API
  async updateEvent(id: string, eventData: any) {
    return this.put(`/events/${id}`, eventData);
  }

  async deleteEvent(id: string) {
    return this.delete(`/events/${id}`);
  }

  async getMyEvents() {
    return this.get('/events/my');
  }

  // Enhanced Forum API
  async updateForumPost(id: string, postData: any) {
    return this.put(`/forum/posts/${id}`, postData);
  }

  async deleteForumPost(id: string) {
    return this.delete(`/forum/posts/${id}`);
  }

  async likeForumPost(id: string) {
    return this.post(`/forum/posts/${id}/like`);
  }

  async unlikeForumPost(id: string) {
    return this.delete(`/forum/posts/${id}/like`);
  }

  async getForumComments(postId: string) {
    return this.get(`/forum/posts/${postId}/comments`);
  }

  async updateForumComment(postId: string, commentId: string, content: string) {
    return this.put(`/forum/posts/${postId}/comments/${commentId}`, { content });
  }

  async deleteForumComment(postId: string, commentId: string) {
    return this.delete(`/forum/posts/${postId}/comments/${commentId}`);
  }

  // Enhanced Business API
  async createBusiness(businessData: any) {
    return this.post('/business', businessData);
  }

  async updateBusiness(id: string, businessData: any) {
    return this.put(`/business/${id}`, businessData);
  }

  async deleteBusiness(id: string) {
    return this.delete(`/business/${id}`);
  }

  async getBusinessReviews(id: string) {
    return this.get(`/business/${id}/reviews`);
  }

  async updateBusinessReview(businessId: string, reviewId: string, rating: number, comment?: string) {
    return this.put(`/business/${businessId}/reviews/${reviewId}`, { rating, comment });
  }

  async deleteBusinessReview(businessId: string, reviewId: string) {
    return this.delete(`/business/${businessId}/reviews/${reviewId}`);
  }

  // Enhanced Marketplace API
  async createProduct(productData: any) {
    return this.post('/marketplace/products', productData);
  }

  async updateProduct(id: string, productData: any) {
    return this.put(`/marketplace/products/${id}`, productData);
  }

  async deleteProduct(id: string) {
    return this.delete(`/marketplace/products/${id}`);
  }

  async updateOrderStatus(id: string, status: string) {
    return this.put(`/marketplace/orders/${id}/status`, { status });
  }

  async cancelOrder(id: string) {
    return this.put(`/marketplace/orders/${id}/cancel`);
  }

  // User management API
  async updateProfile(userData: any) {
    return this.put('/auth/profile', userData);
  }

  async changePassword(currentPassword: string, newPassword: string) {
    return this.put('/auth/password', { currentPassword, newPassword });
  }

  async deleteAccount() {
    return this.delete('/auth/account');
  }

  // Notification API
  async getUserNotifications(params?: {
    page?: number;
    limit?: number;
    unread?: boolean;
    type?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.unread !== undefined) searchParams.set('unread', params.unread.toString());
    if (params?.type) searchParams.set('type', params.type);
    
    const query = searchParams.toString();
    return this.get(`/notifications${query ? `?${query}` : ''}`);
  }

  async markNotificationRead(notificationId: string) {
    return this.put(`/notifications/${notificationId}/read`);
  }

  async markAllNotificationsRead() {
    return this.put('/notifications/read-all');
  }

  async deleteNotification(notificationId: string) {
    return this.delete(`/notifications/${notificationId}`);
  }

  async getNotificationPreferences() {
    return this.get('/notifications/preferences');
  }

  async updateNotificationPreferences(preferences: any) {
    return this.put('/notifications/preferences', preferences);
  }

  async sendTestNotification(type: string) {
    return this.post('/notifications/test', { type });
  }

  // File Upload API
  async uploadFile(file: File, type: 'avatar' | 'document' | 'image' = 'image') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const token = this.getAuthToken();
    
    return fetch(`${this.baseURL}/upload`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: formData,
    }).then(async (response) => {
      const data = await response.json();
      if (!response.ok) {
        throw new ApiError(
          data.message || `Upload failed: ${response.status}`,
          response.status,
          data.code
        );
      }
      return data;
    });
  }

  // Admin API
  async getAdminStats() {
    return this.get('/admin/stats');
  }

  async getAdminActivity(params?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    
    const query = searchParams.toString();
    return this.get(`/admin/activity${query ? `?${query}` : ''}`);
  }

  async getUsers(params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);
    if (params?.role) searchParams.set('role', params.role);
    
    const query = searchParams.toString();
    return this.get(`/admin/users${query ? `?${query}` : ''}`);
  }

  async updateUserRole(userId: string, role: string) {
    return this.put(`/admin/users/${userId}/role`, { role });
  }

  async banUser(userId: string, reason?: string) {
    return this.post(`/admin/users/${userId}/ban`, { reason });
  }

  async unbanUser(userId: string) {
    return this.post(`/admin/users/${userId}/unban`);
  }

  async moderateContent(type: string, id: string, action: string, reason?: string) {
    return this.post(`/admin/moderate`, { type, id, action, reason });
  }

  // Notifications API
  async getNotifications() {
    return this.get('/notifications');
  }

  async markNotificationAsRead(id: string) {
    return this.put(`/notifications/${id}/read`);
  }

  async markAllNotificationsAsRead() {
    return this.put('/notifications/read-all');
  }

  // Jobs API
  async getJobs(params?: {
    category?: string;
    type?: string;
    search?: string;
    page?: number;
    limit?: number;
  }) {
    const searchParams = new URLSearchParams();
    if (params?.category) searchParams.set('category', params.category);
    if (params?.type) searchParams.set('type', params.type);
    if (params?.search) searchParams.set('search', params.search);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    
    const query = searchParams.toString();
    return this.get(`/jobs${query ? `?${query}` : ''}`);
  }

  async getJob(id: string) {
    return this.get(`/jobs/${id}`);
  }

  async createJob(jobData: any) {
    return this.post('/jobs', jobData);
  }

  async updateJob(id: string, jobData: any) {
    return this.put(`/jobs/${id}`, jobData);
  }

  async deleteJob(id: string) {
    return this.delete(`/jobs/${id}`);
  }

  async applyForJob(id: string, applicationData: any) {
    return this.post(`/jobs/${id}/apply`, applicationData);
  }

  // Search API
  async globalSearch(query: string, filters?: {
    types?: string[];
    categories?: string[];
    page?: number;
    limit?: number;
  }) {
    const searchParams = new URLSearchParams();
    searchParams.set('q', query);
    if (filters?.types) searchParams.set('types', filters.types.join(','));
    if (filters?.categories) searchParams.set('categories', filters.categories.join(','));
    if (filters?.page) searchParams.set('page', filters.page.toString());
    if (filters?.limit) searchParams.set('limit', filters.limit.toString());
    
    const queryString = searchParams.toString();
    return this.get(`/search${queryString ? `?${queryString}` : ''}`);
  }

  // Advanced Search API
  async searchContent(params: {
    q?: string;
    type?: 'all' | 'businesses' | 'products' | 'events' | 'forum' | 'news' | 'jobs';
    category?: string;
    location?: string;
    minPrice?: number;
    maxPrice?: number;
    minRating?: number;
    startDate?: string;
    endDate?: string;
    tags?: string[];
    sortBy?: 'relevance' | 'date' | 'rating' | 'price' | 'name' | 'popularity';
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  }) {
    const searchParams = new URLSearchParams();
    
    if (params.q) searchParams.set('q', params.q);
    if (params.type && params.type !== 'all') searchParams.set('type', params.type);
    if (params.category) searchParams.set('category', params.category);
    if (params.location) searchParams.set('location', params.location);
    if (params.minPrice !== undefined) searchParams.set('minPrice', params.minPrice.toString());
    if (params.maxPrice !== undefined) searchParams.set('maxPrice', params.maxPrice.toString());
    if (params.minRating !== undefined) searchParams.set('minRating', params.minRating.toString());
    if (params.startDate) searchParams.set('startDate', params.startDate);
    if (params.endDate) searchParams.set('endDate', params.endDate);
    if (params.tags && params.tags.length > 0) searchParams.set('tags', params.tags.join(','));
    if (params.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);
    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    
    const query = searchParams.toString();
    return this.get(`/search/advanced${query ? `?${query}` : ''}`);
  }

  async getSearchSuggestions(query: string, type?: string) {
    const searchParams = new URLSearchParams();
    searchParams.set('q', query);
    if (type) searchParams.set('type', type);
    
    return this.get(`/search/suggestions?${searchParams.toString()}`);
  }

  async getSearchCategories() {
    return this.get('/search/categories');
  }

  async getPopularSearches(type?: string) {
    const searchParams = new URLSearchParams();
    if (type) searchParams.set('type', type);
    
    const query = searchParams.toString();
    return this.get(`/search/popular${query ? `?${query}` : ''}`);
  }

  // Reviews & Ratings API
  async getReviews(entityType: string, entityId: string, params?: {
    rating?: number;
    sort?: 'recent' | 'oldest' | 'helpful' | 'rating';
    verified?: boolean;
    page?: number;
    limit?: number;
  }) {
    const searchParams = new URLSearchParams();
    if (params?.rating) searchParams.set('rating', params.rating.toString());
    if (params?.sort) searchParams.set('sort', params.sort);
    if (params?.verified) searchParams.set('verified', params.verified.toString());
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    
    const query = searchParams.toString();
    return this.get(`/${entityType}/${entityId}/reviews${query ? `?${query}` : ''}`);
  }

  async createReview(reviewData: {
    entityId: string;
    entityType: string;
    rating: number;
    title: string;
    comment: string;
  }) {
    return this.post('/reviews', reviewData);
  }

  async updateReview(reviewId: string, reviewData: {
    rating?: number;
    title?: string;
    comment?: string;
  }) {
    return this.put(`/reviews/${reviewId}`, reviewData);
  }

  async deleteReview(reviewId: string) {
    return this.delete(`/reviews/${reviewId}`);
  }

  async likeReview(reviewId: string) {
    return this.post(`/reviews/${reviewId}/like`);
  }

  async dislikeReview(reviewId: string) {
    return this.post(`/reviews/${reviewId}/dislike`);
  }

  async reportReview(reviewId: string, reason: string) {
    return this.post(`/reviews/${reviewId}/report`, { reason });
  }

  async replyToReview(reviewId: string, comment: string) {
    return this.post(`/reviews/${reviewId}/reply`, { comment });
  }

  async likeReply(replyId: string) {
    return this.post(`/reviews/reply/${replyId}/like`);
  }

  async deleteReply(replyId: string) {
    return this.delete(`/reviews/reply/${replyId}`);
  }

  async markReviewHelpful(reviewId: string) {
    return this.post(`/reviews/${reviewId}/helpful`);
  }

  async pinReview(reviewId: string) {
    return this.post(`/reviews/${reviewId}/pin`);
  }

  async unpinReview(reviewId: string) {
    return this.delete(`/reviews/${reviewId}/pin`);
  }

  // Messaging API
  async getConversations() {
    return this.get('/conversations');
  }

  async getConversation(conversationId: string) {
    return this.get(`/conversations/${conversationId}`);
  }

  async createConversation(data: {
    type: 'direct' | 'group';
    participantIds: string[];
    title?: string;
  }) {
    return this.post('/conversations', data);
  }

  async updateConversation(conversationId: string, data: {
    title?: string;
    isArchived?: boolean;
    isPinned?: boolean;
    isMuted?: boolean;
  }) {
    return this.put(`/conversations/${conversationId}`, data);
  }

  async deleteConversation(conversationId: string) {
    return this.delete(`/conversations/${conversationId}`);
  }

  async getMessages(conversationId: string, params?: {
    page?: number;
    limit?: number;
    before?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.before) searchParams.set('before', params.before);
    
    const query = searchParams.toString();
    return this.get(`/conversations/${conversationId}/messages${query ? `?${query}` : ''}`);
  }

  async sendMessage(data: {
    conversationId: string;
    content: string;
    type?: 'text' | 'image' | 'file' | 'location' | 'event';
    replyTo?: string;
    metadata?: any;
  }) {
    return this.post('/messages', data);
  }

  async uploadMessageFile(file: File, conversationId: string) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('conversationId', conversationId);

    const token = this.getAuthToken();
    
    return fetch(`${this.baseURL}/messages/upload`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: formData,
    }).then(async (response) => {
      const data = await response.json();
      if (!response.ok) {
        throw new ApiError(
          data.message || `Upload failed: ${response.status}`,
          response.status,
          data.code
        );
      }
      return data;
    });
  }

  async editMessage(messageId: string, content: string) {
    return this.put(`/messages/${messageId}`, { content });
  }

  async deleteMessage(messageId: string) {
    return this.delete(`/messages/${messageId}`);
  }

  async markMessagesAsRead(conversationId: string, messageIds?: string[]) {
    return this.put(`/conversations/${conversationId}/read`, { messageIds });
  }

  async addParticipants(conversationId: string, participantIds: string[]) {
    return this.post(`/conversations/${conversationId}/participants`, { participantIds });
  }

  async removeParticipant(conversationId: string, participantId: string) {
    return this.delete(`/conversations/${conversationId}/participants/${participantId}`);
  }

  async updateParticipantRole(conversationId: string, participantId: string, role: 'admin' | 'member') {
    return this.put(`/conversations/${conversationId}/participants/${participantId}`, { role });
  }

  async searchMessages(query: string, conversationId?: string) {
    const searchParams = new URLSearchParams();
    searchParams.set('q', query);
    if (conversationId) searchParams.set('conversationId', conversationId);
    
    return this.get(`/messages/search?${searchParams.toString()}`);
  }

  async getMessageHistory(conversationId: string, date: string) {
    return this.get(`/conversations/${conversationId}/history?date=${date}`);
  }

  // Utility methods
  setAuthToken(token: string | null) {
    if (typeof window !== 'undefined') {
      if (token) {
        localStorage.setItem('auth_token', token);
      } else {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
      }
    }
  }

  getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token');
    }
    return null;
  }

  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }
}

export const api = new ApiClient();
export default api;