import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get all events with optional filtering
export const getEvents = async (req: Request, res: Response) => {
  try {
    const { category, upcoming, limit } = req.query;
    
    const where: any = {};
    
    // Filter by category if provided
    if (category) {
      where.category = category;
    }
    
    // Filter for upcoming events if requested
    if (upcoming === 'true') {
      where.date = {
        gte: new Date()
      };
    }
    
    const events = await prisma.event.findMany({
      where,
      include: {
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        attendees: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          }
        },
        _count: {
          select: {
            attendees: true
          }
        }
      },
      orderBy: {
        date: 'asc'
      },
      take: limit ? parseInt(limit as string) : undefined
    });

    res.json({
      success: true,
      data: events,
      count: events.length
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des événements'
    });
  }
};

// Get event by ID
export const getEventById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const event = await prisma.event.findUnique({
      where: { id },
      include: {
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
            email: true
          }
        },
        attendees: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          }
        },
        _count: {
          select: {
            attendees: true
          }
        }
      }
    });

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Événement non trouvé'
      });
    }

    res.json({
      success: true,
      data: event
    });
  } catch (error) {
    console.error('Error fetching event:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'événement'
    });
  }
};

// Create new event
export const createEvent = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const {
      title,
      description,
      location,
      date,
      endDate,
      image,
      category,
      maxAttendees,
      isPublic
    } = req.body;

    // Ensure the user is authenticated
    const userId = (req as any).user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const event = await prisma.event.create({
      data: {
        title,
        description,
        location,
        date: new Date(date),
        endDate: endDate ? new Date(endDate) : null,
        image,
        category,
        maxAttendees: maxAttendees ? parseInt(maxAttendees) : null,
        isPublic: isPublic !== undefined ? isPublic : true,
        organizerId: userId
      },
      include: {
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        _count: {
          select: {
            attendees: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Événement créé avec succès',
      data: event
    });
  } catch (error) {
    console.error('Error creating event:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de l\'événement'
    });
  }
};

// Update event
export const updateEvent = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if event exists
    const existingEvent = await prisma.event.findUnique({
      where: { id }
    });

    if (!existingEvent) {
      return res.status(404).json({
        success: false,
        message: 'Événement non trouvé'
      });
    }

    // Check if user is organizer or admin
    if (existingEvent.organizerId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    const updateData: any = {};
    const {
      title,
      description,
      location,
      date,
      endDate,
      image,
      category,
      maxAttendees,
      isPublic
    } = req.body;

    // Only update provided fields
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (location !== undefined) updateData.location = location;
    if (date !== undefined) updateData.date = new Date(date);
    if (endDate !== undefined) updateData.endDate = endDate ? new Date(endDate) : null;
    if (image !== undefined) updateData.image = image;
    if (category !== undefined) updateData.category = category;
    if (maxAttendees !== undefined) updateData.maxAttendees = maxAttendees ? parseInt(maxAttendees) : null;
    if (isPublic !== undefined) updateData.isPublic = isPublic;

    const event = await prisma.event.update({
      where: { id },
      data: updateData,
      include: {
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        _count: {
          select: {
            attendees: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Événement mis à jour avec succès',
      data: event
    });
  } catch (error) {
    console.error('Error updating event:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de l\'événement'
    });
  }
};

// Delete event
export const deleteEvent = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Check if event exists
    const existingEvent = await prisma.event.findUnique({
      where: { id }
    });

    if (!existingEvent) {
      return res.status(404).json({
        success: false,
        message: 'Événement non trouvé'
      });
    }

    // Check if user is organizer or admin
    if (existingEvent.organizerId !== userId && userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    await prisma.event.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Événement supprimé avec succès'
    });
  } catch (error) {
    console.error('Error deleting event:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'événement'
    });
  }
};

// Join/Leave event (RSVP)
export const rsvpEvent = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status } = req.body; // INTERESTED, ATTENDING, NOT_ATTENDING
    const userId = (req as any).user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Check if event exists
    const event = await prisma.event.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            attendees: {
              where: {
                status: 'ATTENDING'
              }
            }
          }
        }
      }
    });

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Événement non trouvé'
      });
    }

    // Check if event is full
    if (event.maxAttendees && event._count.attendees >= event.maxAttendees && status === 'ATTENDING') {
      return res.status(400).json({
        success: false,
        message: 'Événement complet'
      });
    }

    // Check if user already has RSVP
    const existingRsvp = await prisma.eventAttendee.findUnique({
      where: {
        eventId_userId: {
          eventId: id,
          userId: userId
        }
      }
    });

    let rsvp;
    if (existingRsvp) {
      // Update existing RSVP
      rsvp = await prisma.eventAttendee.update({
        where: {
          eventId_userId: {
            eventId: id,
            userId: userId
          }
        },
        data: {
          status: status
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true
            }
          }
        }
      });
    } else {
      // Create new RSVP
      rsvp = await prisma.eventAttendee.create({
        data: {
          eventId: id,
          userId: userId,
          status: status
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true
            }
          }
        }
      });
    }

    res.json({
      success: true,
      message: 'RSVP mis à jour avec succès',
      data: rsvp
    });
  } catch (error) {
    console.error('Error updating RSVP:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du RSVP'
    });
  }
};

// Get event attendees
export const getEventAttendees = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status } = req.query;

    const where: any = { eventId: id };
    if (status) {
      where.status = status;
    }

    const attendees = await prisma.eventAttendee.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      },
      orderBy: {
        joinedAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: attendees,
      count: attendees.length
    });
  } catch (error) {
    console.error('Error fetching attendees:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des participants'
    });
  }
};