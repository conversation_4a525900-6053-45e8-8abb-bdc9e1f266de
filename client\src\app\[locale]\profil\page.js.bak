'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Settings, 
  ShoppingBag, 
  MessageSquare, 
  Heart,
  Edit,
  Camera,
  Star,
  Clock,
  Package,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useApi } from '@/hooks/useApi';
import api from '@/lib/api';

interface UserStats {
  totalOrders: number;
  totalSpent: number;
  forumPosts: number;
  forumComments: number;
  reviewsGiven: number;
  averageRating: number;
}

interface RecentOrder {
  id: string;
  date: string;
  total: number;
  status: 'pending' | 'confirmed' | 'delivered' | 'cancelled';
  itemCount: number;
  business: string;
}

interface RecentActivity {
  id: string;
  type: 'order' | 'forum_post' | 'review' | 'comment';
  title: string;
  date: string;
  link: string;
}

export default function ProfilePage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // API hooks
  const {
    data: stats,
    loading: statsLoading,
    error: statsError,
    execute: fetchStats
  } = useApi<UserStats>();

  const {
    data: recentOrders,
    loading: ordersLoading,
    error: ordersError,
    execute: fetchOrders
  } = useApi<RecentOrder[]>();

  const {
    data: recentActivity,
    loading: activityLoading,
    error: activityError,
    execute: fetchActivity
  } = useApi<RecentActivity[]>();

  const loading = statsLoading || ordersLoading || activityLoading;
  const error = statsError || ordersError || activityError;


  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/connexion');
      return;
    }

    // Load profile data in parallel
    Promise.all([
      fetchStats(() => api.get('/user/stats')),
      fetchOrders(() => api.getUserOrders()),
      fetchActivity(() => api.get('/user/activity'))
    ]);
  }, [isAuthenticated, router]);

  const mockRecentActivity: RecentActivity[] = [
    {
      id: '1',
      type: 'review',
      title: 'Avis sur "Pain de campagne artisanal"',
      date: '2024-01-15',
      link: '/marche/produit/1'
    },
    {
      id: '2',
      type: 'forum_post',
      title: 'Nouveau sujet: "Travaux rue de la Paix"',
      date: '2024-01-14',
      link: '/forum/sujet/2'
    },
    {
      id: '3',
      type: 'order',
      title: 'Commande #001 livrée',
      date: '2024-01-12',
      link: '/marche/commandes/1'
    }
  ];

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/connexion');
      return;
    }

  }, [isAuthenticated, router]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'text-green-600 bg-green-50';
      case 'confirmed':
        return 'text-blue-600 bg-blue-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'Livrée';
      case 'confirmed':
        return 'Confirmée';
      case 'pending':
        return 'En attente';
      case 'cancelled':
        return 'Annulée';
      default:
        return status;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <Package size={16} />;
      case 'forum_post':
        return <MessageSquare size={16} />;
      case 'review':
        return <Star size={16} />;
      case 'comment':
        return <MessageSquare size={16} />;
      default:
        return <Clock size={16} />;
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Connexion requise</h1>
          <p className="text-gray-600 mb-6">Vous devez être connecté pour accéder à votre profil.</p>
          <Link
            href="/connexion"
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            Se connecter
          </Link>
        </div>
      </div>
    );
  }

  if (loading || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
            <div className="flex items-center space-x-6">
              <div className="w-24 h-24 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-1 w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Profile Header */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center space-x-6 mb-6 md:mb-0">
              <div className="relative">
                <div className="w-24 h-24 bg-primary-100 rounded-full flex items-center justify-center">
                  {user.avatar ? (
                    <img src={user.avatar} alt={user.firstName} className="w-24 h-24 rounded-full object-cover" />
                  ) : (
                    <span className="text-primary-600 font-bold text-2xl">
                      {user.firstName[0]}{user.lastName[0]}
                    </span>
                  )}
                </div>
                <button className="absolute bottom-0 right-0 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center hover:bg-primary-700 transition-colors">
                  <Camera size={16} />
                </button>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {user.firstName} {user.lastName}
                </h1>
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Mail size={14} />
                    <span>{user.email}</span>
                  </div>
                  {user.phone && (
                    <div className="flex items-center space-x-2">
                      <Phone size={14} />
                      <span>{user.phone}</span>
                    </div>
                  )}
                  {user.address && (
                    <div className="flex items-center space-x-2">
                      <MapPin size={14} />
                      <span>{user.address}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2">
                    <Calendar size={14} />
                    <span>Membre depuis {new Date(user.createdAt).toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' })}</span>
                  </div>
                </div>
              </div>
            </div>
            <Link
              href="/profil/modifier"
              className="flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
            >
              <Edit size={18} />
              <span>Modifier le profil</span>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <ShoppingBag size={24} className="text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
                  <p className="text-sm text-gray-600">Commandes</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Package size={24} className="text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalSpent.toFixed(2)}€</p>
                  <p className="text-sm text-gray-600">Dépensé</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <MessageSquare size={24} className="text-orange-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stats.forumPosts}</p>
                  <p className="text-sm text-gray-600">Sujets forum</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Star size={24} className="text-yellow-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stats.averageRating}</p>
                  <p className="text-sm text-gray-600">Note moyenne</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="border-b">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Vue d'ensemble
              </button>
              <button
                onClick={() => setActiveTab('orders')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'orders'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Mes commandes
              </button>
              <button
                onClick={() => setActiveTab('activity')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'activity'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Activité récente
              </button>
              <button
                onClick={() => setActiveTab('wishlist')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'wishlist'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Liste de souhaits
              </button>
            </nav>
          </div>

          <div className="p-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Commandes récentes</h3>
                  <div className="space-y-4">
                    {recentOrders?.slice(0, 3).map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">Commande #{order.id}</p>
                          <p className="text-sm text-gray-600">{order.business} • {order.itemCount} article{order.itemCount > 1 ? 's' : ''}</p>
                          <p className="text-sm text-gray-500">{new Date(order.date).toLocaleDateString('fr-FR')}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{order.total.toFixed(2)}€</p>
                          <span className={`inline-block px-2 py-1 text-xs rounded-full ${getStatusColor(order.status)}`}>
                            {getStatusText(order.status)}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Link
                    href="/profil?tab=orders"
                    className="inline-block mt-4 text-primary-600 hover:text-primary-700 text-sm font-medium"
                  >
                    Voir toutes les commandes →
                  </Link>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité récente</h3>
                  <div className="space-y-4">
                    {recentActivity?.slice(0, 5).map((activity) => (
                      <div key={activity.id} className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1">
                          <Link
                            href={activity.link}
                            className="text-sm text-gray-900 hover:text-primary-600"
                          >
                            {activity.title}
                          </Link>
                          <p className="text-xs text-gray-500">
                            {new Date(activity.date).toLocaleDateString('fr-FR')}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Orders Tab */}
            {activeTab === 'orders' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Mes commandes</h3>
                  <Link
                    href="/marche"
                    className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm"
                  >
                    Nouvelle commande
                  </Link>
                </div>
                <div className="space-y-4">
                  {recentOrders?.map((order) => (
                    <div key={order.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h4 className="font-medium text-gray-900">Commande #{order.id}</h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {new Date(order.date).toLocaleDateString('fr-FR', {
                              day: 'numeric',
                              month: 'long',
                              year: 'numeric'
                            })}
                          </p>
                        </div>
                        <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor(order.status)}`}>
                          {getStatusText(order.status)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-600">{order.business}</p>
                          <p className="text-sm text-gray-500">{order.itemCount} article{order.itemCount > 1 ? 's' : ''}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{order.total.toFixed(2)}€</p>
                          <Link
                            href={`/marche/commandes/${order.id}`}
                            className="text-sm text-primary-600 hover:text-primary-700"
                          >
                            Voir détails
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Activity Tab */}
            {activeTab === 'activity' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Activité récente</h3>
                <div className="space-y-4">
                  {recentActivity?.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <Link
                          href={activity.link}
                          className="font-medium text-gray-900 hover:text-primary-600"
                        >
                          {activity.title}
                        </Link>
                        <p className="text-sm text-gray-500 mt-1">
                          {new Date(activity.date).toLocaleDateString('fr-FR', {
                            day: 'numeric',
                            month: 'long',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Wishlist Tab */}
            {activeTab === 'wishlist' && (
              <div className="text-center py-12">
                <Heart size={64} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Votre liste de souhaits est vide</h3>
                <p className="text-gray-600 mb-6">Ajoutez vos produits favoris à votre liste de souhaits.</p>
                <Link
                  href="/marche"
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  Découvrir les produits
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}