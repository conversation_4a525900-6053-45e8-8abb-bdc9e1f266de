export default function AdminEvenementsPage() {
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Gestion des Événements</h1>
      
      <div className="mb-6">
        <a 
          href="/fr/admin/evenements/nouveau" 
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Nouvel événement
        </a>
      </div>
      
      <div className="space-y-4">
        <div className="border p-4 rounded-lg">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold">Marché de Noël 2024</h3>
              <p className="text-gray-600">Venez découvrir notre marché de Noël traditionnel avec des produits locaux...</p>
              <div className="text-sm text-gray-500 mt-2">
                <span>📅 15 décembre 2024</span> • <span>📍 Place du marché</span> • <span>👥 45 inscrits</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <button className="text-blue-600 hover:underline">Modifier</button>
              <button className="text-red-600 hover:underline">Supprimer</button>
            </div>
          </div>
        </div>
        
        <div className="border p-4 rounded-lg">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold">Cours de yoga en plein air</h3>
              <p className="text-gray-600">Séance de yoga pour tous les niveaux dans le parc municipal...</p>
              <div className="text-sm text-gray-500 mt-2">
                <span>📅 Tous les samedis</span> • <span>📍 Parc municipal</span> • <span>👥 12 inscrits</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <button className="text-blue-600 hover:underline">Modifier</button>
              <button className="text-red-600 hover:underline">Supprimer</button>
            </div>
          </div>
        </div>
        
        <div className="border p-4 rounded-lg">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold">Atelier jardinage urbain</h3>
              <p className="text-gray-600">Apprenez les bases du jardinage en ville avec nos experts...</p>
              <div className="text-sm text-gray-500 mt-2">
                <span>📅 20 janvier 2025</span> • <span>📍 Centre communautaire</span> • <span>👥 8 inscrits</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <button className="text-blue-600 hover:underline">Modifier</button>
              <button className="text-red-600 hover:underline">Supprimer</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}