# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Plateforme LeClub - Communauté & Marché Local

Une plateforme française combinant communication communautaire et marché local pour renforcer les liens communautaires et soutenir les entreprises locales.

## Architecture & Structure

### Monorepo Structure
- **Root**: Coordonne les workspaces avec scripts npm et concurrently
- **client/**: Application Next.js 14 avec internationalization française (next-intl)
- **server/**: API Express.js avec Prisma ORM et authentification JWT
- **shared/**: Types et utilitaires partagés (prévu)
- **docs/**: Documentation du projet

### Tech Stack
- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS + next-intl (français)
- **Backend**: Express.js + TypeScript + Prisma + SQLite (dev) / PostgreSQL (prod)
- **Real-time**: Socket.io pour notifications et mises à jour live
- **Auth**: JWT avec bcrypt
- **Payments**: Stripe (prévu)

### Database Models (Prisma)
Le schéma supporte les entités principales:
- **User**: Résidents, propriétaires d'entreprises, admins avec rôles
- **Business**: Répertoire d'entreprises avec profils riches
- **Product**: Marketplace local avec gestion stock
- **News**: Actualités et annonces officielles
- **Event**: Calendrier communautaire avec RSVP
- **Forum**: Discussions catégorisées avec modération
- **Order**: Système e-commerce avec statuts
- **JobPosting**: Offres d'emploi local
- **Notification**: Alertes temps réel (urgence, événements, etc.)

## Commands

### Development
```bash
npm run dev              # Démarre client (3000) + serveur (3001) avec concurrently
npm run dev:client       # Client Next.js uniquement
npm run dev:server       # Serveur Express uniquement
```

### Build & Production
```bash
npm run build           # Build client + serveur
npm run build:client    # Build Next.js
npm run build:server    # Compile TypeScript serveur
npm start               # Démarre serveur production
```

### Database (Prisma + Docker PostgreSQL)
```bash
# Docker PostgreSQL
npm run docker:up       # Démarrer PostgreSQL
npm run docker:down     # Arrêter conteneurs
npm run docker:reset    # Reset complet avec données
npm run docker:admin    # Démarrer pgAdmin (http://localhost:5050)

# Prisma
npm run db:migrate      # Créer nouvelle migration
npm run db:reset        # Reset complet schema + données
npm run db:studio       # Interface admin base de données
cd server && npm run seed  # Données de test
```

### Testing & Quality
```bash
npm test                # Tests client + serveur
npm run test:client     # Tests Jest/React Testing Library
npm run test:server     # Tests API avec Jest
npm run lint            # ESLint client + serveur
cd client && npm run type-check  # Vérification TypeScript client
```

## Internationalization (i18n)

### French-First Configuration
- **Primary locale**: `fr` (français)
- **Messages**: `client/messages/fr.json` avec structure hiérarchique
- **Routing**: URL format `/[locale]/...` avec middleware next-intl
- **Components**: `useTranslations()` hook pour textes

### Message Structure
```typescript
// Organisé par domaine fonctionnel
{
  "common": { "welcome": "Bienvenue", ... },
  "navigation": { "home": "Accueil", ... },
  "news": { "title": "Actualités", ... },
  "marketplace": { "products": "Produits", ... }
}
```

## Authentication & Security

### JWT Implementation
- **Generation**: `generateToken(id)` dans authController
- **Middleware**: `protect` pour routes authentifiées, `authorize(roles)` pour permissions
- **Storage**: Headers `Authorization: Bearer <token>`

### Security Layers
- **Rate limiting**: 100 req/15min par IP
- **Helmet**: Headers sécurisés
- **CORS**: Configuré pour frontend localhost:3000
- **Input validation**: express-validator sur routes API

## API Routes Structure

### Base URL: `/api`
- **auth**: `/register`, `/login`, `/profile` (JWT)
- **news**: CRUD actualités (admin only pour création)
- **events**: Gestion événements avec RSVP
- **forum**: Posts, commentaires, catégories
- **business**: Répertoire entreprises
- **marketplace**: Produits, commandes, panier
- **jobs**: Offres emploi
- **user**: Notifications, commandes, avis utilisateur

## Development Notes

### Environment Setup
- **Database**: PostgreSQL avec Docker (docker-compose.yml)
- **Environment**: `.env` configuré pour Docker PostgreSQL, `.env.local` pour client
- **Module Resolution**: `tsconfig-paths` configuré pour imports `@/`
- **Docker**: Container PostgreSQL + pgAdmin optionnel

### Component Patterns
- **Layout**: Header/Footer dans `layout.tsx` avec next-intl provider
- **Icons**: Lucide React pour icônes cohérentes
- **Styling**: Tailwind avec classes utilitaires, couleurs primary/secondary configurées
- **Data**: Mock data dans composants en attendant API complète

### Real-time Features
- **Socket.io**: Configuré pour notifications d'urgence et mises à jour forum
- **Rooms**: `community-{id}` pour alertes, `forum-{id}` pour discussions
- **Events**: `join-community`, `join-forum` pour abonnements

### Database Features (PostgreSQL)
- **Full Schema**: Enums typés, JSON natif, Decimal précis, String arrays
- **Extensions**: uuid-ossp, pg_trgm (recherche), unaccent (français)
- **Seed Data**: Utilisateurs, entreprises, produits, actualités, événements
- **Development**: Docker avec persistance, pgAdmin pour administration