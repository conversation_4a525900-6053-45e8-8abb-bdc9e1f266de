'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { ArrowRight, Users, Store, Calendar, MessageSquare } from 'lucide-react';

export function Hero() {
  const t = useTranslations('home');

  return (
    <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <h1 className="text-4xl md:text-5xl font-bold leading-tight">
              {t('title')}
            </h1>
            <p className="text-xl text-primary-100">
              {t('subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/inscription"
                className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2"
              >
                <span>Rejoindre la communauté</span>
                <ArrowRight size={20} />
              </Link>
              <Link
                href="/marche"
                className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors text-center"
              >
                Explorer le marché
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Users className="mx-auto mb-4" size={32} />
              <h3 className="font-semibold mb-2">Communauté</h3>
              <p className="text-sm text-primary-100">Connectez-vous avec vos voisins</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Store className="mx-auto mb-4" size={32} />
              <h3 className="font-semibold mb-2">Marché Local</h3>
              <p className="text-sm text-primary-100">Soutenez les commerces locaux</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Calendar className="mx-auto mb-4" size={32} />
              <h3 className="font-semibold mb-2">Événements</h3>
              <p className="text-sm text-primary-100">Participez à la vie locale</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <MessageSquare className="mx-auto mb-4" size={32} />
              <h3 className="font-semibold mb-2">Discussions</h3>
              <p className="text-sm text-primary-100">Participez aux débats</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}